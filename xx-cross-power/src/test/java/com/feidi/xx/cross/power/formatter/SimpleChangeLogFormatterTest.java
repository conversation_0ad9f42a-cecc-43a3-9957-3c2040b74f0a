package com.feidi.xx.cross.power.formatter;

import com.feidi.xx.cross.power.XXCrossPowerApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * SimpleChangeLogFormatter 测试类
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@SpringBootTest(classes = XXCrossPowerApplication.class)
class SimpleChangeLogFormatterTest {

    @Autowired
    private SimpleChangeLogFormatter formatter;

    private ChangeLogContext testContext;

    @BeforeEach
    void setUp() {
        testContext = ChangeLogContext.builder()
                .main("张三")
                .fieldName("groupId")
                .fieldDisplayName("司机组")
                .oldValue("1935591284225773569")
                .newValue("1930168299662778370")
                .operator("管理员")
                .build();
    }

    @Test
    void testSpringFunctionFormat() {
        // 测试Spring容器中的格式化函数
        String template = "将 {main} 的{fieldDisplayName}从 {#driverGroup.format(#oldValue)} 变更为 {#driverGroup.format(#newValue)}";
        String result = formatter.format(template, testContext);

        System.out.println("Spring函数格式化测试: " + result);
    }
}
