package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowDriverAccount;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import lombok.experimental.Accessors;

/**
 * 司机账户业务对象 pow_driver_account
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriverAccount.class, reverseConvertGenerate = false)
public class PowDriverAccountBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机
     */
    @NotNull(message = "司机不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String account;

    /**
     * 类型
     */
    private String type;

    /**
     * 验证资金
     */
    private String verifyAmount;

    /**
     * 是否默认
     */
    private String defaulted;

    /**
     * 短信验证码值
     */
    //@NotBlank(message = "短信验证码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String smsCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
