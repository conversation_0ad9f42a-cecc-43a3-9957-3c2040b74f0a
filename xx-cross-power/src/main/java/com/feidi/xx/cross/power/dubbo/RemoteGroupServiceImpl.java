package com.feidi.xx.cross.power.dubbo;

import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteGroupService;
import com.feidi.xx.cross.power.api.domain.group.vo.RemoteGroupVo;
import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.cross.power.mapper.PowGroupMapper;
import com.feidi.xx.cross.power.service.IPowGroupService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 司机组服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteGroupServiceImpl implements RemoteGroupService {

    private final IPowGroupService powGroupService;
    private final PowGroupMapper powGroupMapper;

    /**
     * 获取司机组信息
     * @param groupId
     * @return
     */
    @Override
    public RemoteGroupVo getGroupInfoByGroupId(Long groupId) {
        PowGroup group = powGroupService.getGroupInfo(groupId);
        return BeanUtils.copyProperties(group, RemoteGroupVo.class);
    }

    /**
     * 获取所有司机组信息
     *
     * @return 司机组信息
     */
    @Override
    public List<RemoteGroupVo> queryAll() {
        List<PowGroup> powGroups = powGroupMapper.selectList();
        return BeanUtils.copyToList(powGroups, RemoteGroupVo.class);
    }
}
