package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.bo.PowCarBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 车辆Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowCarService {

    /**
     * 查询车辆
     *
     * @param id 主键
     * @return 车辆
     */
    PowCarVo queryById(Long id);

    /**
     * 查询车辆
     *
     * @param driverId 司机主键
     * @return 车辆
     */
    PowCarVo queryByDriver(Long driverId);

    /**
     * 批量查询车辆
     * @param ids
     * @return
     */
    List<PowCarVo> queryByDriverIds(List<Long> ids);

    /**
     * 分页查询车辆列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 车辆分页列表
     */
    TableDataInfo<PowCarVo> queryPageList(PowCarBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的车辆列表
     *
     * @param bo 查询条件
     * @return 车辆列表
     */
    List<PowCarVo> queryList(PowCarBo bo);

    /**
     * 新增车辆
     *
     * @param bo 车辆
     * @return 是否新增成功
     */
    Boolean insertByBo(PowCarBo bo);

    /**
     * 修改车辆
     *
     * @param bo 车辆
     * @return 是否修改成功
     */
    Boolean updateByBo(PowCarBo bo);

    /**
     * 校验并批量删除车辆信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
