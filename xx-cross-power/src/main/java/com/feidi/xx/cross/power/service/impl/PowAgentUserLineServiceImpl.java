package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.power.domain.*;
import com.feidi.xx.cross.power.domain.pojo.bo.PowAgentUserLineForm;
import com.feidi.xx.cross.power.mapper.PowAgentLineMapper;
import com.feidi.xx.cross.power.mapper.PowAgentUserLineMapper;
import com.feidi.xx.cross.power.service.IPowAgentUserLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 代理商用户线路Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class PowAgentUserLineServiceImpl implements IPowAgentUserLineService {

    private final PowAgentUserLineMapper agentUserLineMapper;

    private final PowAgentLineMapper agentLineMapper;

    /**
     * 分配线路给代理商调度用户
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignAgentUser(PowAgentUserLineForm bo) {
        Long agentId = LoginHelper.getAgentId();
        Assert.isTrue(UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType()), "当前接口仅限代理商使用");

        List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(agentId);
        List<Long> lineIds = agentLines.stream().map(PowAgentLine::getLineId).toList();
        Assert.isTrue(CollUtil.contains(lineIds, bo.getLineId()), "只能设置代理商用户所属代理商所分配到的线路");

        //清空线路 目前一条线路分配一个代理商
        agentUserLineMapper.deleteByLineId(bo.getLineId());

        if (CollUtil.isNotEmpty(bo.getAgentUserIds())) {
            List<PowAgentUserLine> addLines = new ArrayList<>();
            for (Long agentUserId : bo.getAgentUserIds()) {
                PowAgentUserLine powAgentUserLine = new PowAgentUserLine(agentUserId, 0L, bo.getLineId());
                addLines.add(powAgentUserLine);
            }
            return agentUserLineMapper.insertBatch(addLines);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignLine(PowAgentUserLineForm bo) {
        Long agentId = LoginHelper.getAgentId();
        Assert.isTrue(UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType()), "当前接口仅限代理商使用");

        List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(agentId);
        List<Long> lineIds = agentLines.stream().map(PowAgentLine::getLineId).toList();
        Assert.isTrue(CollUtil.containsAll(lineIds, bo.getLineIds()), "只能设置司机所属代理商所分配到的线路");

        //清空线路 目前一条线路分配一个代理商
        agentUserLineMapper.deleteByAgentUserId(bo.getAgentUserId());

        if (CollUtil.isNotEmpty(bo.getLineIds())) {
            List<PowAgentUserLine> addLines = new ArrayList<>();
            for (Long lineId : bo.getLineIds()) {
                PowAgentUserLine powAgentUserLine = new PowAgentUserLine(bo.getAgentUserId(), 0L, lineId);
                addLines.add(powAgentUserLine);
            }
            return agentUserLineMapper.insertBatch(addLines);
        }
        return true;
    }

    @Override
    public List<Long> listByAgentUserId(Long agentUserId) {
        List<PowAgentUserLine> agentUserLines = agentUserLineMapper.listByAgentUserId(agentUserId);
        return StreamUtils.toList(agentUserLines, PowAgentUserLine::getLineId);
    }
}
