package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowDriverCertsVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverCertsBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 司机证件Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowDriverCertsService {

    /**
     * 查询司机证件
     *
     * @param id 主键
     * @return 司机证件
     */
    PowDriverCertsVo queryById(Long id);

    /**
     * 分页查询司机证件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机证件分页列表
     */
    TableDataInfo<PowDriverCertsVo> queryPageList(PowDriverCertsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机证件列表
     *
     * @param bo 查询条件
     * @return 司机证件列表
     */
    List<PowDriverCertsVo> queryList(PowDriverCertsBo bo);

    /**
     *  获取司机证件
     * @param driverId
     * @return
     */
    List<PowDriverCertsVo> queryListByDriver(Long driverId);

    /**
     * 新增司机证件
     *
     * @param bo 司机证件
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverCertsBo bo);

    /**
     * 修改司机证件
     *
     * @param bo 司机证件
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverCertsBo bo);

    /**
     * 校验并批量删除司机证件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
