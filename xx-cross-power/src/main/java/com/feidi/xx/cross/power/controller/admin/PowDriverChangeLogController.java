package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowDriverChangeLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverChangeLogVo;
import com.feidi.xx.cross.power.service.IPowDriverChangeLogService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机信息变更记录
 * 前端访问路由地址为:/power/driverChangeLog
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driverChangeLog")
public class PowDriverChangeLogController extends BaseController {

    private final IPowDriverChangeLogService powDriverChangeLogService;

    /**
     * 查询司机信息变更记录列表
     */
    @SaCheckPermission("power:driverChangeLog:list")
    @GetMapping("/list")
    public TableDataInfo<PowDriverChangeLogVo> list(PowDriverChangeLogBo bo, PageQuery pageQuery) {
        return powDriverChangeLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机信息变更记录列表
     */
    @SaCheckPermission("power:driverChangeLog:export")
    @Log(title = "司机信息变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Download(name = "司机信息变更记录", module = ModuleConstants.POWER, mode = "no")
    public Object export(@RequestBody PowDriverChangeLogBo bo, HttpServletResponse response) {
        List<PowDriverChangeLogVo> list = powDriverChangeLogService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "司机信息变更记录", PowDriverChangeLogVo.class, outputStream);
        return outputStream.toByteArray();
    }
}
