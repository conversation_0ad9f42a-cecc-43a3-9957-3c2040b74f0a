package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowAgentRate;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 代理商佣金比例业务对象 pow_agent_rate
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAgentRate.class, reverseConvertGenerate = false)
public class PowAgentRateBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 代理商
     */
    @NotNull(message = "代理商不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 佣金比例
     */
    @NotNull(message = "佣金比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal rate;

    /**
     * 状态
     */
    private String status;
}
