package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.api.domain.line.bo.RemoteAgentLineBo;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteLineAgentBo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代理商列表Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAgentService {

    /**
     * 查询代理商列表
     *
     * @param id 主键
     * @return 代理商列表
     */
    PowAgentVo queryById(Long id);

    /**
     * 获取代理子账号
     *
     * @param agentId
     * @return
     */
    List<PowAgentVo> queryChildrenByAgentId(Long agentId);

    /**
     * 分页查询代理商列表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商列表分页列表
     */
    TableDataInfo<PowAgentVo> queryPageList(PowAgentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代理商列表列表
     *
     * @param bo 查询条件
     * @return 代理商列表列表
     */
    List<PowAgentVo> queryList(PowAgentBo bo);

    /**
     * 查询符合条件的代理商列表
     *
     * @param bo 查询条件
     * @return 代理商列表
     */
    List<PowAgentVo> queryAllList(PowAgentBo bo);

    /**
     * 新增代理商列表
     *
     * @param bo 代理商列表
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAgentBo bo);

    /**
     * 修改代理商列表
     *
     * @param bo 代理商列表
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAgentBo bo);

    /**
     * 修改代理商状态
     * @param id
     * @param status
     * @return
     */
    Boolean updateStatus(Long id, String status);

    /**
     * 校验并批量删除代理商列表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 启用代理
     * @param agentId
     */
    void enableAgent(Long agentId);

    /**
     * 禁用代理
     * @param agentId
     */
    void disableAgent(long agentId);

    /**
     * 禁用代理的某些线路
     * @param agentId
     * @param lineIds
     */
    void disableLineOfAgent(Long agentId, List<Long> lineIds);

    /**
     * 禁用线路的某些代理
     * @param lineId
     * @param agentIds
     */
    void disableAgentOfLine(Long lineId, List<Long> agentIds);


    /**
     * 给子代理商分配线路
     * @param bo
     * @return
     */
    boolean assignLine2child(RemoteAgentLineBo bo);

    /**
     * 根据代理ID查询线路ID
     * @param agentId
     * @return
     */
    List<Long> listByAgentId(Long agentId);

    /**
     * 根据线路ID查询代理ID
     * @param lineId
     * @return
     */
    List<Long> listByLineId(Long lineId);

    /**
     * 给代理分配线路
     * @param bo
     * @return
     */
    boolean assignLine(RemoteAgentLineBo bo);

    /**
     * 给线路分配代理
     * @param bo
     * @return
     */
    boolean assignAgent(RemoteLineAgentBo bo);

    /**
     * 根据代理商ID查询代理信息，并缓存
     *
     * @param id 代理商ID
     * @return 代理信息
     */
    PowAgent queryByIdAndCacheInfo(Long id);

    /**
     * 根据代理商ID查询代理信息，并缓存
     *
     * @param ids 代理商ID集合
     * @return 代理信息
     */
    List<PowAgent> queryByIdsAndCacheInfo(List<Long> ids);

}
