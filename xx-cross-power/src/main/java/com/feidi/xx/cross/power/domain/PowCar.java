package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 车辆对象 pow_car
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_car")
public class PowCar extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 司机
     */
    private Long driverId;

    /**
     * 车辆图片
     */
    private String carPic;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆品牌
     */
    private String carBrand;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆颜色
     */
    private String carColor;

    /**
     * 车辆样式
     */
    private String carStyle;

    /**
     * 驱动方式
     */
    private String driveType;

    /**
     * 车辆级别
     */
    private String level;

    /**
     * 车辆识别码（车架号）
     */
    private String vin;

    /**
     * 发动机编号
     */
    private String engine;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 座位数
     */
    private Long seat;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
