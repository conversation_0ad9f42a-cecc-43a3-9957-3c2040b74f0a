package com.feidi.xx.cross.power.controller.mobi;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.service.IPowAgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 后台 - 代理商列表
 * 前端访问路由地址为:/power/mobi/agent
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MOBI_ROUTE_PREFIX + "/agent")
public class MobiAgentController extends BaseController {

    private final IPowAgentService powAgentService;

    private final PowAgentMapper baseMapper;


    /**
     * 查询代理商列表-全部
     * 父子关系
     */
    @SaCheckPermission("power:powerAgent:list")
    @GetMapping("/list/all")
    public R<List<PowAgentVo>> list(PowAgentBo bo) {
        bo.setParentId(0L);
        List<PowAgentVo> vos = powAgentService.queryAllList(bo);
        if (CollUtil.isNotEmpty(vos)) {
            List<Long> pids = StreamUtils.toList(vos, PowAgentVo::getId);
            List<PowAgent> parents = baseMapper.listByParentIds(pids);
            if (CollUtil.isNotEmpty(parents)) {
                List<PowAgentVo> convert = MapstructUtils.convert(parents, PowAgentVo.class);
                Map<Long, List<PowAgentVo>> parentMap = StreamUtils.groupByKey(convert, PowAgentVo::getParentId);
                for (PowAgentVo vo : vos) {
                    vo.setChildren(parentMap.get(vo.getId()));
                }
            }
        }
        return R.ok(vos);
    }
}
