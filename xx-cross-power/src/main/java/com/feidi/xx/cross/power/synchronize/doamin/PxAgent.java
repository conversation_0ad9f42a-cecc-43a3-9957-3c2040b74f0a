package com.feidi.xx.cross.power.synchronize.doamin;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 代理商对象 px_agent
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "px_agent", autoResultMap = true)
public class PxAgent extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * 头像
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String avatar;

    /**
     * 营业执照ossId
     */
    private Long businessLicense;

    /**
     * 法人身份证正面ossId
     */
    private Long frontLegalCard;

    /**
     * 法人身份证背面ossId
     */
    private Long backLegalCard;

    /**
     * 主体类型
     */
    private String mainType;

    /**
     * 主体名称
     */
    private String mainBody;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 开户行
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String bank;

    /**
     * 支行
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String subBank;

    /**
     * 账号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String bankNo;

    /**
     * 省ID
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Long provinceId;

    /**
     * 省
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String province;

    /**
     * 城市ID
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Long cityId;

    /**
     * 城市编码
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String cityCode;

    /**
     * 市
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String city;

    /**
     * 区域ID
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Long districtId;

    /**
     * 区域编码
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String adCode;

    /**
     * 区域
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String district;

    /**
     * 联系地址
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String address;

    /**
     * 自动抢单状态 {@link com.feidi.xx.common.core.enums.IsYesEnum}
     */
    private String isAutoRob;

    /**
     * 状态
     */
    private String status;

    /**
     * 保证金
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Long earnest;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 招商地址
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String inviteUrl;

    /**
     * 招商广告
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String inviteImage;

    /**
     * 备注
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
