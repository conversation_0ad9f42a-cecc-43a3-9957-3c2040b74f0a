package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 代理商线路对象 pow_agent_user_line
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_agent_user_line")
public class PowAgentUserLine extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 路线
     */
    private Long lineId;

    /**
     * 代理商用户
     */
    private Long agentUserId;

    /**
     * 父代理商ID
     */
    private Long parentId;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    public PowAgentUserLine(Long agentUserId, Long parentId, Long lineId) {
        this.agentUserId = agentUserId;
        this.parentId = parentId;
        this.lineId = lineId;
    }
}
