package com.feidi.xx.cross.power.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.power.domain.PowDriverLine;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverLineVo;


import java.util.Collection;
import java.util.List;

/**
 * 司机线路Mapper接口
 */
public interface PowDriverLineMapper extends BaseMapperPlus<PowDriverLine, PowDriverLineVo> {

    default List<PowDriverLine> listByDriverId(Long driverId) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getDriverId, driverId);
        return selectList(lqw);
    }

    default List<PowDriverLine> listByLineId(Long lineId) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getLineId, lineId);
        return selectList(lqw);
    }

    default List<PowDriverLine> listByAgentId(Long agentId) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getAgentId, agentId);
        return selectList(lqw);
    }

    default List<PowDriverLine> listByDriverIds(Collection<Long> driverIds) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .in(CollUtil.isNotEmpty(driverIds), PowDriverLine::getDriverId, driverIds);
        return selectList(lqw);
    }

    default List<PowDriverLine> listByLineIds(Collection<Long> lineIds) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .in(CollUtil.isNotEmpty(lineIds), PowDriverLine::getLineId, lineIds);
        return selectList(lqw);
    }

    default List<PowDriverLine> listByLineIds(Long agentId, Collection<Long> lineIds) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getAgentId, agentId)
                .in(CollUtil.isNotEmpty(lineIds), PowDriverLine::getLineId, lineIds);
        return selectList(lqw);
    }

    default boolean deleteByLineId(Long lineId) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getLineId, lineId)) > 0;
    }

    default boolean deleteByDriverId(Long driverId) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getDriverId, driverId)) > 0;
    }

    default boolean deleteByAgentId(Long agentId) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getAgentId, agentId)) > 0;
    }

    default boolean deleteByDriverIds(Collection<Long> driverIds) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .in(CollUtil.isNotEmpty(driverIds), PowDriverLine::getDriverId, driverIds)) > 0;
    }

    default boolean deleteByAgentIds(Collection<Long> agentIds) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .in(CollUtil.isNotEmpty(agentIds), PowDriverLine::getAgentId, agentIds)) > 0;
    }

    default boolean deleteByAgentIds(Long lineId, Collection<Long> agentIds) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getLineId, lineId)
                .in(CollUtil.isNotEmpty(agentIds), PowDriverLine::getAgentId, agentIds)) > 0;
    }

    default boolean deleteByLineIds(Collection<Long> lineIds) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .in(CollUtil.isNotEmpty(lineIds), PowDriverLine::getAgentId, lineIds)) > 0;
    }

    default boolean deleteByLineIds(Long agentId, Collection<Long> lineIds) {
        return delete(new LambdaQueryWrapper<PowDriverLine>()
                .eq(PowDriverLine::getAgentId, agentId)
                .in(CollUtil.isNotEmpty(lineIds), PowDriverLine::getLineId, lineIds)) > 0;
    }

}
