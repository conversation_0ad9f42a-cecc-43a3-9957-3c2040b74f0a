package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 代理商城市业务对象 pow_agent_city
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PowAgentCityBatchBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 开城
     */
    private Long cityId;

    /**
     * 代理商城市集合
     */
    List<PowAgentCityBo> agentCityBos;

}
