package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.feidi.xx.common.core.constant.TenantConstants;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvCertStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvCertTypeEnum;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.power.domain.PowAuditRecord;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowDriverCerts;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.PowRejectReason;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverAgreementForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverCarForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverDrivingForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverIdCardForm;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverAgreementVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverCarVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverIdCardVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDrvDrivingVo;
import com.feidi.xx.cross.power.domain.vo.PowAuditRecordVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.mapper.*;
import com.feidi.xx.cross.power.service.IPowDriverApplyService;
import com.feidi.xx.cross.power.service.IPowGroupService;
import com.feidi.xx.cross.power.util.ImgCompress;
import com.feidi.xx.resource.api.RemoteFileService;
import com.feidi.xx.resource.api.RemoteVirtualPhoneService;
import com.feidi.xx.resource.api.domain.RemoteFile;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 司机申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PowDriverApplyServiceImpl implements IPowDriverApplyService {

    private final PowDriverMapper baseMapper;

    private final PowDriverCertsMapper driverCertsMapper;

    private final PowCarMapper carMapper;

    private final PowAuditRecordMapper auditRecordMapper;

    private final PowDriverRateMapper driverRateMapper;

    private final IPowGroupService groupService;

    @DubboReference
    private final RemoteFileService remoteFileService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteVirtualPhoneService virPhoneService;

    /**
     * 司机申请 - 上传身份证
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadIdCard(PowDriverIdCardForm bo) {
        validateBeforeUpdate(bo);
        // 身份信息更新
        PowDriver powDriver = MapstructUtils.convert(bo, PowDriver.class);
        powDriver.setId(bo.getDriverId());
        powDriver.setCardNo(bo.getCarNo());
        baseMapper.updateById(powDriver);
        // 删除之前的证件信息
        this.deleteDriverCertsByDriverIdAndTypes(bo.getDriverId(), Stream.of(DrvCertTypeEnum.ID_FRONT.getCode(), DrvCertTypeEnum.ID_BACK.getCode(),
                DrvCertTypeEnum.AUTH.getCode()).toList());

        //证件入库
        handleDriverCerts(bo.getDriverId(), bo.getFrontOssId(), bo.getBackOssId(), DrvCertTypeEnum.ID_FRONT, DrvCertTypeEnum.ID_BACK, bo.getName(), null, null, bo.getEndTime(), bo.getAddress());
        //认证图片入库
        handleDriverCerts(bo.getDriverId(), bo.getAuthOssId(), null, DrvCertTypeEnum.AUTH, null, bo.getName(), null, null, null, null);
        return true;
    }

    private static void validateBeforeUpdate(PowDriverIdCardForm bo) {
        String birthday = bo.getBirthday();
        Date endTime = bo.getEndTime();
        if (birthday != null && endTime != null) {
            if (DateUtils.parseDate(birthday).after(endTime)) {
                throw new ServiceException("出生年月不能大于有效截止日期");
            }
            if (endTime.toInstant().isBefore(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant())) {
                throw new ServiceException("证件内容已过期,请更新证件后重新上传");
            }
        }
    }

    /**
     * 司机申请 - 上传驾驶证
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadDriving(PowDriverDrivingForm bo) {
        //司机信息更新
        if (bo.getFirstTime().after(bo.getEndTime())) {
            throw new ServiceException("首次领证日期不能大于有效截止日期");
        }
        PowDriver driver = BeanUtils.copyProperties(bo, PowDriver.class);
        driver.setId(bo.getDriverId());
        baseMapper.updateById(driver);

        // 删除之前的证件信息
        this.deleteDriverCertsByDriverIdAndTypes(bo.getDriverId(), Stream.of(DrvCertTypeEnum.DRIVING.getCode(), DrvCertTypeEnum.DRIVING_BACK.getCode()).toList());

        // 证件入库
        handleDriverCerts(bo.getDriverId(), bo.getFrontOssId(), bo.getBackOssId(), DrvCertTypeEnum.DRIVING, DrvCertTypeEnum.DRIVING_BACK, bo.getCertsOwner(), bo.getFirstTime(), null, bo.getEndTime(), null);
        return true;
    }

    /**
     * 司机申请 - 上传车辆信息
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadCar(PowDriverCarForm bo) {
        Date firstTime = bo.getFirstTime();
        Date startTime = bo.getStartTime();

        if (firstTime != null && startTime != null) {
            if (firstTime.after(startTime)) {
                throw new ServiceException("注册日期不能大于发证日期");
            }
        }
        //更新司机信息
        PowDriver driver = BeanUtils.copyProperties(bo, PowDriver.class);
        driver.setId(bo.getDriverId());
        baseMapper.updateById(driver);
        //更新车辆信息
        PowCar powCar = BeanUtils.copyProperties(bo, PowCar.class);
        powCar.setCarPic(getImgUrlByOssId(bo.getCarOssId()));
        powCar.setStatus(StatusEnum.ENABLE.getCode());
        powCar.setTenantId(TenantConstants.DEFAULT_TENANT_ID);
        //车辆表存在记录则更新 不存在删除
        Long l = carMapper.selectCount(new LambdaQueryWrapper<PowCar>().eq(PowCar::getDriverId, bo.getDriverId()));
        if (l > 0) {
            carMapper.updateByDriverId(powCar);
        } else {
            carMapper.insert(powCar);
        }

        // 删除之前的证件信息
        this.deleteDriverCertsByDriverIdAndTypes(bo.getDriverId(), Stream.of(DrvCertTypeEnum.LICENSE.getCode(), DrvCertTypeEnum.LICENSE_BACK.getCode(), DrvCertTypeEnum.CAR.getCode(), DrvCertTypeEnum.POLICY.getCode()).toList());

        // 证件入库
        handleDriverCerts(bo.getDriverId(), bo.getTravelFrontOssId(), bo.getTravelBackOssId(), DrvCertTypeEnum.LICENSE, DrvCertTypeEnum.LICENSE_BACK, bo.getCarOwner(), bo.getFirstTime(), bo.getStartTime(), null, null);
        handleDriverCerts(bo.getDriverId(), bo.getInsuranceOssId(), null, DrvCertTypeEnum.POLICY, null, bo.getCarOwner(), null, null, null, null);
        handleDriverCerts(bo.getDriverId(), bo.getCarOssId(), null, DrvCertTypeEnum.CAR, null, bo.getCarOwner(), null, null, null, null);
        return true;
    }

    /**
     * 司机申请 - 上传代扣协议
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadAgreement(PowDriverAgreementForm bo) {
        //自营司机校验代扣信息
        if(bo.getType() != null && DriverTypeEnum.SELF.getCode().equals(bo.getType())){
            if (bo.getAgreementOssId() == null) {
                throw new ServiceException("自营司机-代扣协议图片不能为空");
            }
            if (bo.getHeldIdCardOssId() == null) {
                throw new ServiceException("自营司机-手持身份证图片不能为空");
            }
            if (bo.getHeldAgreementOssId() == null) {
                throw new ServiceException("自营司机-手持代扣协议图片不能为空");
            }
            // 删除之前的证件信息
            this.deleteDriverCertsByDriverIdAndTypes(bo.getDriverId(), Stream.of(DrvCertTypeEnum.AGREEMENT.getCode(), DrvCertTypeEnum.HELD_ID.getCode(), DrvCertTypeEnum.HELD_AGREEMENT.getCode()).toList());
            //自营司机图片入库
            if (DriverTypeEnum.SELF.getCode().equalsIgnoreCase(bo.getType())) {
                handleDriverCerts(bo.getDriverId(), bo.getAgreementOssId(), null, DrvCertTypeEnum.AGREEMENT, null, null, null, null, null, null);
                handleDriverCerts(bo.getDriverId(), bo.getHeldIdCardOssId(), bo.getHeldAgreementOssId(), DrvCertTypeEnum.HELD_ID, DrvCertTypeEnum.HELD_AGREEMENT, null, null, null, null, null);
            }
        }
        return true;
    }

    /**
     * 司机申请
     *
     * @param bo 司机申请bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean apply(PowDriverBo bo) {
        Long driverId = LoginHelper.getUserId();
        //校验之前上传的证件信息是否完整
        PowDriverVo applyVo = getStorage(driverId);
        if (Objects.equals(applyVo.getIdCardVo().getStatus(), DrvCertStatusEnum.UPLOAD.getCode()) ||
                Objects.equals(applyVo.getDrivingVo().getStatus(), DrvCertStatusEnum.UPLOAD.getCode()) ||
                Objects.equals(applyVo.getCarVo().getStatus(), DrvCertStatusEnum.UPLOAD.getCode())) {
            throw new ServiceException("请完善证件信息");
        }

        log.info("加盟类型{}",bo.getType());
        if (DriverTypeEnum.SELF.getCode().equals(bo.getType())) {
            if (Objects.equals(applyVo.getAgreementVo().getStatus(), DrvCertStatusEnum.UPLOAD.getCode())) {
                throw new ServiceException("请完善代扣协议信息");
            }
        }else{
            // 删除之前的证件信息
            log.info("非自营-删除代扣协议");
            this.deleteDriverCertsByDriverIdAndTypes(driverId, Stream.of(DrvCertTypeEnum.AGREEMENT.getCode(), DrvCertTypeEnum.HELD_ID.getCode(), DrvCertTypeEnum.HELD_AGREEMENT.getCode()).toList());
        }

        if (StringUtil.isBlank(bo.getCityCode())) {
            throw new ServiceException("请选择常驻城市");
        }

        //图片审核 结果不阻塞流程
        this.uploadAuthImg(applyVo.getIdCardVo().getAuthOssId());
        PowDriver driver = baseMapper.selectById(driverId);
        driver.setCityCode(bo.getCityCode());
        driver.setSource(bo.getSource());
        if (Objects.equals(driver.getAuditStatus(), DrvAuditStatusEnum.ING.getCode()) ||
                Objects.equals(driver.getAuditStatus(), DrvAuditStatusEnum.SUCCESS.getCode())) {
            throw new ServiceException("您已是司机或在审核中……");
        }
        LambdaUpdateWrapper<PowDriver> lqw = new LambdaUpdateWrapper<>();
        //司机类型
        if (driver.getGroupId() != null) {
            PowGroupVo powGroup = groupService.queryById(driver.getGroupId());
            if (ObjectUtils.isNotNull(powGroup)) {
                lqw.eq(PowDriver::getId, driverId)
                        .set(PowDriver::getType, powGroup.getType());
            }
        }
        log.info("加盟类型{}",bo.getType());
        lqw.eq(PowDriver::getId, driverId)
                .set(PowDriver::getStatus, UserStatusEnum.OK.getCode())
                .set(PowDriver::getAuditStatus, DrvAuditStatusEnum.ING.getCode())
                .set(PowDriver::getSubmitTime, DateUtil.date())
                .set(bo.getType() != null, PowDriver::getType, bo.getType())
                .set(PowDriver::getAuditTime, null);
        //封装司机信息
        log.info("sql{}", lqw.getSqlComment());
        baseMapper.update(driver, lqw);
        //删除缓存数据
        deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.create(driverId));
        deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.create(driverId));
        deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.create(driverId));
        deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.create(driverId));
        return true;
    }

    /**
     * 司机申请暂存数据
     *
     * @param bo 暂存对象
     */
    @Override
    public Boolean storage(PowDriverBo bo) {

        // 缓存身份信息
        String identityKey = PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.create(LoginHelper.getUserId());
        RedisUtils.setCacheObject(identityKey, bo.getIdCardBo(), PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.getDuration());

        // 缓存驾驶证信息
        String drivingKey = PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.create(LoginHelper.getUserId());
        RedisUtils.setCacheObject(drivingKey, bo.getDrivingBo(), PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.getDuration());

        // 缓存车辆信息
        String carKey = PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.create(LoginHelper.getUserId());
        RedisUtils.setCacheObject(carKey, bo.getCarBo(), PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.getDuration());

        if (DriverTypeEnum.SELF.getCode().equals(bo.getType())){
            // 缓存代扣信息
            String agreementKey = PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.create(LoginHelper.getUserId());
            RedisUtils.setCacheObject(agreementKey, bo.getAgreementBo(), PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.getDuration());
        }
        return true;
    }

    /**
     * 获取暂存数据
     *
     * @return 暂存Vo对象
     */
    @Override
    public PowDriverVo getStorage(Long driverId) {
        PowDriverVo applyVo = new PowDriverVo();
        if (getTemporaryCache().getIdCardVo() != null) {
            setCertsInfoByCache(applyVo);
        } else {
            setCertsInfo(applyVo, driverId);
        }
        return applyVo;
    }

    /**
     * 获取司机加盟结果
     *
     * @return
     */
    @Override
    public PowDriverVo getResult() {
        PowDriver driver = baseMapper.selectById(LoginHelper.getUserId());
        PowDriverVo driverVo = new PowDriverVo();
        if (Objects.equals(driver.getAuditStatus(), DrvAuditStatusEnum.SUCCESS.getCode())) {
            driverVo.setAuditStatus(driver.getAuditStatus());
            driverVo.setType(driver.getType());
            return driverVo;
        } else {
            // 获取最新的审核记录
            PowAuditRecordVo auditRecordVo = auditRecordMapper.selectVoOne(new LambdaQueryWrapper<PowAuditRecord>()
                    .eq(PowAuditRecord::getDriverId, LoginHelper.getUserId())
                    .orderByDesc(PowAuditRecord::getCreateTime).last("limit 1"));
            if (ObjectUtil.isAllNotEmpty(auditRecordVo)) {
                List<PowRejectReason> reasonsList = JSONUtil.toList(auditRecordVo.getReasons(), PowRejectReason.class);
                List<String> reasons = reasonsList.stream().map(PowRejectReason::getReason).flatMap(Collection::stream).toList();
                driverVo.setRejectReason(reasons);
                driverVo.setAuditStatus(driver.getAuditStatus());
            }
            driverVo.setType(driver.getType());
            return driverVo;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadAuthImg(Long ossId) {
        PowDriver driver = baseMapper.selectById(LoginHelper.getUserId());
        if (IsYesEnum.YES.getCode().equals(driver.getLegal())){
            return true;
        }
        List<RemoteFile> remoteFile = remoteFileService.selectByIds(ossId.toString());
        String base64;
        try {
            base64 = ImgCompress.compressImageToSize(remoteFile.get(0).getName(), remoteFile.get(0).getUrl(), 25);
        } catch (Exception e) {
            log.error(e.fillInStackTrace().getMessage());
            throw new ServiceException("图片压缩失败");
        }
        //byte[] bytes = HttpUtil.downloadBytes(url);
        //String base64 = Base64.encode(bytes);
        Boolean auth = false;
        try{
            auth = virPhoneService.auth(driver.getName(), driver.getCardNo(), driver.getPhone(), base64);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        if (auth){
            driver.setLegal(IsYesEnum.YES.getCode());
            baseMapper.updateById(driver);
            // 删除之前的证件信息
            this.deleteDriverCertsByDriverIdAndTypes(driver.getId(), Stream.of(DrvCertTypeEnum.AUTH.getCode()).toList());
            driverCertsMapper.insert(createDriverCert(driver.getId(), ossId, DrvCertTypeEnum.AUTH, driver.getName(), null, null, null, null));
        }
        return auth;

    }

    /**
     * 处理司机证件通用方法
     */
    private void handleDriverCerts(Long driverId, Long frontOssId, Long backOssId, DrvCertTypeEnum frontType, DrvCertTypeEnum backType,
                                   String certsOwner, Date firstTime, Date startTime, Date endTime, String address) {
        List<PowDriverCerts> PowDriverCertsList = new ArrayList<>();

        if (frontOssId != null && frontOssId != 0) {
            PowDriverCertsList.add(createDriverCert(driverId, frontOssId, frontType, certsOwner, firstTime, startTime, endTime, address));
        }
        if (backOssId != null && backOssId != 0) {
            PowDriverCertsList.add(createDriverCert(driverId, backOssId, backType, certsOwner, firstTime, startTime, endTime, address));
        }

        if (CollUtil.isNotEmpty(PowDriverCertsList)) {
            driverCertsMapper.insertBatch(PowDriverCertsList);
        }
    }

    private PowDriverCerts createDriverCert(Long driverId, Long ossId, DrvCertTypeEnum type, String certsOwner, Date firstTime, Date startTime, Date endTime, String address) {
        PowDriverCerts cert = new PowDriverCerts();
        cert.setTenantId(TenantConstants.DEFAULT_TENANT_ID);
        cert.setDriverId(driverId);
        cert.setType(type.getCode());
        cert.setOssId(ossId);
        cert.setCertsOwner(certsOwner);
        cert.setFirstTime(firstTime);
        cert.setStartTime(startTime);
        cert.setEndTime(endTime);
        cert.setAddress(address);
        cert.setStatus(StatusEnum.ENABLE.getCode());
        return cert;
    }


    /**
     * 根据ossId获取相应图片url
     *
     * @param ossId ossId
     * @return
     */
    public String getImgUrlByOssId(Long ossId) {
        // 使用LambdaQueryWrapper构建查询条件，查询文件表中匹配给定ossId的记录
        String ossIds = String.valueOf(ossId);
        List<RemoteFile> remoteFiles = remoteFileService.selectByIds(ossIds);
        return remoteFiles == null || remoteFiles.isEmpty() ? null : remoteFiles.get(0).getUrl();
    }

    /**
     * 批量获取图片URL（ossId -> url）
     *
     * @param ossIds ossId列表
     * @return Map<ossId, 对应url>
     */
    public Map<Long, String> getImgUrlMapByOssIds(List<Long> ossIds) {
        if (CollectionUtils.isEmpty(ossIds)) {
            return new HashMap<>();
        }
        // 将Long列表转换为逗号分隔的字符串
        String idsStr = ossIds.stream().map(String::valueOf).collect(Collectors.joining(","));

        List<RemoteFile> remoteFiles = remoteFileService.selectByIds(idsStr);

        return Optional.ofNullable(remoteFiles)
                .orElse(Collections.emptyList())
                .stream()
                .filter(file -> file.getUrl() != null)
                .collect(Collectors.toMap(RemoteFile::getOssId, RemoteFile::getUrl, (existing, replacement) -> existing));
    }

    /**
     * 查缓存
     *
     * @return 缓存信息
     */
    public PowDriverVo getTemporaryCache() {
        PowDriverVo applyVo = new PowDriverVo();
        if (RedisUtils.hasKey(PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.create(LoginHelper.getUserId()))) {
            PowDriverIdCardForm cacheIdentity = RedisUtils.getCacheObject(PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.create(LoginHelper.getUserId()));
            PowDriverIdCardVo identityVo = BeanUtils.copyProperties(cacheIdentity, PowDriverIdCardVo.class);
            applyVo.setIdCardVo(identityVo);
        }
        if (RedisUtils.hasKey(PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.create(LoginHelper.getUserId()))) {
            PowDriverDrivingForm cacheLicense = RedisUtils.getCacheObject(PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.create(LoginHelper.getUserId()));
            PowDrvDrivingVo licenseVo = BeanUtils.copyProperties(cacheLicense, PowDrvDrivingVo.class);
            applyVo.setDrivingVo(licenseVo);
        }
        if (RedisUtils.hasKey(PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.create(LoginHelper.getUserId()))) {
            PowDriverCarForm cacheCar = RedisUtils.getCacheObject(PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.create(LoginHelper.getUserId()));
            PowDriverCarVo carVo = BeanUtils.copyProperties(cacheCar, PowDriverCarVo.class);
            applyVo.setCarVo(carVo);
        }
        if (RedisUtils.hasKey(PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.create(LoginHelper.getUserId()))) {
            PowDriverAgreementForm cacheAgreement = RedisUtils.getCacheObject(PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.create(LoginHelper.getUserId()));
            PowDriverAgreementVo carVo = BeanUtils.copyProperties(cacheAgreement, PowDriverAgreementVo.class);
            applyVo.setAgreementVo(carVo);
        }
        return applyVo;
    }

    /**
     * 清除缓存
     */
    public void deleteTemporaryData(String key) {
        if (RedisUtils.hasKey(key)) {
            RedisUtils.deleteObject(key);
            log.info("{}删除成功", key);
        }
    }

    /**
     * 删除司机证件通过司机id与证件type
     *
     * @param driverId 司机id
     * @param types    证件类型
     */
    public void deleteDriverCertsByDriverIdAndTypes(Long driverId, List<String> types) {
        // 根据司机id查数据库记录防止重复提交,若存在则删除重新上传
        LambdaQueryWrapper<PowDriverCerts> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PowDriverCerts::getDriverId, driverId)
                .in(PowDriverCerts::getType, types);
        // 删除旧记录
        driverCertsMapper.delete(lqw);
    }

    /**
     * 用缓存封装证件信息
     *
     * @param applyVo
     */
    private void setCertsInfoByCache(PowDriverVo applyVo) {

        PowDriverIdCardVo identityCache = getTemporaryCache().getIdCardVo();
        if (identityCache != null) {
            PowDriverIdCardVo identityVo = BeanUtils.copyProperties(identityCache, PowDriverIdCardVo.class);
            identityVo.setDesensitizedName(DesensitizedUtil.chineseName(identityCache.getName()));
            identityVo.setDesensitizedCarNo(DesensitizedUtil.idCardNum(identityCache.getCarNo(), 3, 5));
            identityVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
            identityVo.setFrontImg(getImgUrlByOssId(identityCache.getFrontOssId()));
            identityVo.setBackImg(getImgUrlByOssId(identityCache.getBackOssId()));
            identityVo.setAuthImg(getImgUrlByOssId(identityCache.getAuthOssId()));
            applyVo.setIdCardVo(identityVo);
        }

        PowDrvDrivingVo drivingCache = getTemporaryCache().getDrivingVo();
        if (drivingCache != null) {
            PowDrvDrivingVo licenseVo = BeanUtils.copyProperties(drivingCache, PowDrvDrivingVo.class);
            licenseVo.setDesensitizedName(DesensitizedUtil.chineseName(drivingCache.getCertsOwner()));
            licenseVo.setDesensitizedApprovedType(drivingCache.getApprovedType());
            licenseVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
            licenseVo.setFrontImg(getImgUrlByOssId(drivingCache.getFrontOssId()));
            licenseVo.setBackImg(getImgUrlByOssId(drivingCache.getBackOssId()));
            applyVo.setDrivingVo(licenseVo);
        }

        PowDriverCarVo carCache = getTemporaryCache().getCarVo();
        if (carCache != null) {
            PowDriverCarVo carVo = BeanUtils.copyProperties(carCache, PowDriverCarVo.class);
            carVo.setDesensitizedLicensePlate(carCache.getCarNumber());
            carVo.setDesensitizedCarBrand(carCache.getCarBrand());
            carVo.setDesensitizedCarModel(carCache.getCarModel());
            carVo.setDesensitizedCarColor(carCache.getCarColor());
            carVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
            carVo.setLicenseFrontImg(getImgUrlByOssId(carCache.getTravelFrontOssId()));
            carVo.setLicenseBackImg(getImgUrlByOssId(carCache.getTravelBackOssId()));
            carVo.setPolicyImg(getImgUrlByOssId(carCache.getInsuranceOssId()));
            applyVo.setCarVo(carVo);
        }

        PowDriverAgreementVo agreementCache = getTemporaryCache().getAgreementVo();
        if (agreementCache != null) {
            PowDriverAgreementVo agreementVo = BeanUtils.copyProperties(carCache, PowDriverAgreementVo.class);
            agreementVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
            agreementVo.setAgreementImg(getImgUrlByOssId(agreementCache.getAgreementOssId()));
            agreementVo.setHeldIdCardImg(getImgUrlByOssId(agreementCache.getHeldIdCardOssId()));
            agreementVo.setHeldAgreementImg(getImgUrlByOssId(agreementCache.getHeldAgreementOssId()));
            applyVo.setAgreementVo(agreementVo);
        }
    }

    /**
     * 封装证件信息
     */
    private void setCertsInfo(PowDriverVo applyVo, Long driverId) {
        // 批量获取证件信息列表
        Map<String, PowDriverCerts> type2CertInfoMap = listCertInfoById(driverId);
        // 身份信息
        PowDriverCerts idFrontCert = type2CertInfoMap.get(DrvCertTypeEnum.ID_FRONT.getCode());
        PowDriverCerts idBackCert = type2CertInfoMap.get(DrvCertTypeEnum.ID_BACK.getCode());
        PowDriverCerts drivingFrontCert = type2CertInfoMap.get(DrvCertTypeEnum.DRIVING.getCode());
        PowDriverCerts drivingBackCert = type2CertInfoMap.get(DrvCertTypeEnum.DRIVING_BACK.getCode());
        PowDriverCerts carFrontCert = type2CertInfoMap.get(DrvCertTypeEnum.LICENSE.getCode());
        PowDriverCerts carBackCert = type2CertInfoMap.get(DrvCertTypeEnum.LICENSE_BACK.getCode());
        PowDriverCerts carPolicy = type2CertInfoMap.get(DrvCertTypeEnum.POLICY.getCode());
        PowDriverCerts carInfo = type2CertInfoMap.get(DrvCertTypeEnum.CAR.getCode());
        PowDriverCerts agreement =  type2CertInfoMap.get(DrvCertTypeEnum.AGREEMENT.getCode());
        PowDriverCerts heldId = type2CertInfoMap.get(DrvCertTypeEnum.HELD_ID.getCode());
        PowDriverCerts heldAgreement = type2CertInfoMap.get(DrvCertTypeEnum.HELD_AGREEMENT.getCode());
        PowDriverCerts authCert = type2CertInfoMap.get(DrvCertTypeEnum.AUTH.getCode());
        // 批量获取图片url地址
        List<Long> ossIds = Stream.of(idFrontCert, idBackCert, drivingFrontCert, drivingBackCert, carFrontCert, carBackCert, carPolicy, carInfo,
                        agreement, heldId, heldAgreement, authCert)
                .map(cert -> cert != null ? cert.getOssId() : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, String> ossId2UrlMap = getImgUrlMapByOssIds(ossIds);

        PowDriver driver = baseMapper.selectById(driverId);

        PowDriverVo powDriverVo = BeanUtils.copyProperties(driver, PowDriverVo.class);
        applyVo.setDriverVo(powDriverVo);
        applyVo.setType(driver.getType());

        // 处理身份信息
        PowDriverIdCardVo identityVo = processIdentityInfo(idFrontCert, idBackCert, authCert, driver, ossId2UrlMap);
        applyVo.setIdCardVo(identityVo);

        // 处理驾驶证信息
        PowDrvDrivingVo licenseVo = processDrivingInfo(drivingFrontCert, drivingBackCert, driver, ossId2UrlMap);
        applyVo.setDrivingVo(licenseVo);

        // 处理车辆信息
        PowDriverCarVo carVo = processCarInfo(driverId, carFrontCert, carBackCert, carPolicy, carInfo, ossId2UrlMap);
        applyVo.setCarVo(carVo);

        // 处理代扣协议信息
        PowDriverAgreementVo agreementVo = processAgreementInfo(agreement, heldId, heldAgreement, ossId2UrlMap);
        applyVo.setAgreementVo(agreementVo);
    }

    private PowDriverIdCardVo processIdentityInfo(PowDriverCerts idFrontCert, PowDriverCerts idBackCert, PowDriverCerts authCert, PowDriver driver, Map<Long, String> ossId2UrlMap) {
        PowDriverIdCardVo identityVo = BeanUtils.copyProperties(driver, PowDriverIdCardVo.class);
        if (isCertValid(idFrontCert)) {
            fillIdentityVo(identityVo, driver, idFrontCert, idBackCert, authCert, ossId2UrlMap);
            identityVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
        } else {
            identityVo.setStatus(DrvCertStatusEnum.UPLOAD.getCode());
        }
        return identityVo;
    }

    private PowDrvDrivingVo processDrivingInfo(PowDriverCerts drivingFrontCert, PowDriverCerts drivingBackCert, PowDriver driver, Map<Long, String> ossId2UrlMap) {
        PowDrvDrivingVo licenseVo = new PowDrvDrivingVo();

        if (isCertValid(drivingFrontCert)) {
            fillDrivingVo(licenseVo, driver, drivingFrontCert, drivingBackCert, ossId2UrlMap);
            licenseVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
        } else {
            licenseVo.setStatus(DrvCertStatusEnum.UPLOAD.getCode());
        }
        return licenseVo;
    }

    private PowDriverCarVo processCarInfo(Long driverId, PowDriverCerts carFrontCert, PowDriverCerts carBackCert, PowDriverCerts carPolicy, PowDriverCerts carInfo, Map<Long, String> ossId2UrlMap) {
        PowCar car = carMapper.selectOne(new LambdaQueryWrapper<PowCar>().eq(PowCar::getDriverId, driverId));
        PowDriverCarVo carVo = new PowDriverCarVo();
        //设置车辆信息脱敏
        if(car != null){
            fillCarDesensitizedInfo(carVo, car);
        }

        if (isCertValid(carFrontCert)) {
            fillCarVo(carVo,car,carFrontCert, carBackCert, carPolicy, carInfo, ossId2UrlMap);
            carVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
        } else {
            carVo.setStatus(DrvCertStatusEnum.UPLOAD.getCode());
        }
        return carVo;
    }

    private PowDriverAgreementVo processAgreementInfo(PowDriverCerts agreementCert, PowDriverCerts heldIdCert, PowDriverCerts heldAgreementCert, Map<Long, String> ossId2UrlMap) {
        PowDriverAgreementVo agreementVo = new PowDriverAgreementVo();
        if (isCertValid(agreementCert)
                && isCertValid(heldIdCert)
                && isCertValid(heldAgreementCert)) {
            fillAgreementVo(agreementVo, agreementCert, heldIdCert, heldAgreementCert, ossId2UrlMap);
            agreementVo.setStatus(DrvCertStatusEnum.MODIFY.getCode());
        } else {
            agreementVo.setStatus(DrvCertStatusEnum.UPLOAD.getCode());
        }
        return agreementVo;
    }

    // 检查证件是否有效
    private boolean isCertValid(PowDriverCerts cert) {
        return cert != null && cert.getOssId() != null;
    }

    private void fillIdentityVo(PowDriverIdCardVo identityVo, PowDriver driver, PowDriverCerts idFrontCert, PowDriverCerts idBackCert, PowDriverCerts authCert, Map<Long, String> ossId2UrlMap) {
        identityVo.setDesensitizedCarNo(DesensitizedUtil.idCardNum(driver.getCardNo(), 3, 5));
        identityVo.setDesensitizedName(DesensitizedUtil.chineseName(idFrontCert.getCertsOwner()));
        identityVo.setName(idFrontCert.getCertsOwner());
        identityVo.setCarNo(driver.getCardNo());
        identityVo.setEndTime(idBackCert.getEndTime());
        identityVo.setBackOssId(idBackCert.getOssId());
        identityVo.setFrontOssId(idFrontCert.getOssId());
        identityVo.setAuthOssId(authCert == null ? null : authCert.getOssId());
        identityVo.setFrontImg(getImageFromMap(idFrontCert.getOssId(), ossId2UrlMap));
        identityVo.setBackImg(getImageFromMap(idBackCert.getOssId(), ossId2UrlMap));
        identityVo.setAuthImg(authCert == null ? null : getImageFromMap(authCert.getOssId(), ossId2UrlMap));
    }

    private void fillDrivingVo(PowDrvDrivingVo licenseVo, PowDriver driver, PowDriverCerts drivingFrontCert, PowDriverCerts drivingBackCert, Map<Long, String> ossId2UrlMap) {
        licenseVo.setDesensitizedApprovedType(driver.getApprovedType());
        licenseVo.setDesensitizedName(DesensitizedUtil.chineseName(drivingFrontCert.getCertsOwner()));
        licenseVo.setFirstTime(drivingFrontCert.getFirstTime());
        licenseVo.setEndTime(drivingFrontCert.getEndTime());
        licenseVo.setFrontOssId(drivingFrontCert.getOssId());
        licenseVo.setCertsOwner(drivingFrontCert.getCertsOwner());
        licenseVo.setFrontImg(getImageFromMap(drivingFrontCert.getOssId(), ossId2UrlMap));
        licenseVo.setBackOssId(drivingBackCert.getOssId());
        licenseVo.setBackImg(getImageFromMap(drivingBackCert.getOssId(), ossId2UrlMap));
        licenseVo.setApprovedType(driver.getApprovedType());
    }

    private void fillCarVo(PowDriverCarVo carVo, PowCar car, PowDriverCerts carFrontCert, PowDriverCerts carBackCert, PowDriverCerts carPolicy, PowDriverCerts carInfo, Map<Long, String> ossId2UrlMap) {
        if (car != null) {
            carVo.setSeat(car.getSeat());
            carVo.setEngine(car.getEngine());
            carVo.setDriveType(car.getDriveType());
            carVo.setCarNumber(car.getCarNumber());
            carVo.setCarBrand(car.getCarBrand());
            carVo.setCarModel(car.getCarModel());
            carVo.setCarColor(car.getCarColor());
            carVo.setCarStyle(car.getCarStyle());
            carVo.setVin(car.getVin());
        }
        carVo.setCarOwner(carFrontCert.getCertsOwner());
        carVo.setFirstTime(carFrontCert.getFirstTime());
        carVo.setStartTime(carFrontCert.getStartTime());
        carVo.setTravelFrontOssId(carFrontCert.getOssId());
        carVo.setTravelBackOssId(carBackCert.getOssId());
        carVo.setInsuranceOssId(carPolicy.getOssId());
        carVo.setCarOssId(carInfo.getOssId());
        carVo.setLicenseFrontImg(getImageFromMap(carFrontCert.getOssId(), ossId2UrlMap));
        carVo.setLicenseBackImg(getImageFromMap(carBackCert.getOssId(), ossId2UrlMap));
        carVo.setPolicyImg(getImageFromMap(carPolicy.getOssId(), ossId2UrlMap));
        carVo.setCarPic(getImageFromMap(carInfo.getOssId(), ossId2UrlMap));
    }

    private void fillAgreementVo(PowDriverAgreementVo agreementVo, PowDriverCerts agreementCert, PowDriverCerts heldIdCert, PowDriverCerts heldAgreementCert, Map<Long, String> ossId2UrlMap) {
        agreementVo.setAgreementOssId(agreementCert == null ? null : agreementCert.getOssId());
        agreementVo.setHeldIdCardOssId(heldIdCert == null ? null : heldIdCert.getOssId());
        agreementVo.setHeldAgreementOssId(heldAgreementCert == null ? null : heldAgreementCert.getOssId());
        agreementVo.setAgreementImg(agreementCert == null ? null : getImageFromMap(agreementCert.getOssId(), ossId2UrlMap));
        agreementVo.setHeldIdCardImg(heldIdCert == null ? null : getImageFromMap(heldIdCert.getOssId(), ossId2UrlMap));
        agreementVo.setHeldAgreementImg(heldAgreementCert == null ? null : getImageFromMap(heldAgreementCert.getOssId(), ossId2UrlMap));
    }

    // 设置车辆脱敏信息
    private void fillCarDesensitizedInfo(PowDriverCarVo carVo, PowCar car) {
        carVo.setDesensitizedLicensePlate(car.getCarNumber());
        carVo.setDesensitizedCarBrand(car.getCarBrand());
        carVo.setDesensitizedCarModel(car.getCarModel());
        carVo.setDesensitizedCarColor(car.getCarColor());
    }

    // 从 Map 中获取图片
    private String getImageFromMap(Long ossId, Map<Long, String> ossId2UrlMap) {
        return ossId2UrlMap.get(ossId);
    }

    /**
     * 批量获取司机证件信息
     *
     * @param id 司机id
     * @return 返回匹配的司机证书信息对象
     */
    public Map<String, PowDriverCerts> listCertInfoById(Long id) {
        // 使用LambdaQueryWrapper构建查询条件，查询司机证书表中匹配给定司机ID和证书类型的记录
        List<PowDriverCerts> powDriverCerts = driverCertsMapper.selectList(new LambdaQueryWrapper<PowDriverCerts>()
                .eq(PowDriverCerts::getDriverId, id));
        return powDriverCerts.stream().collect(Collectors.toMap(PowDriverCerts::getType, Function.identity()));
    }

}
