package com.feidi.xx.cross.power.dubbo;

import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.cross.common.enums.power.DriverConsentSceneEnum;
import com.feidi.xx.cross.power.api.RemoteDriverConsentLogService;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowDriverConsentLog;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.mapper.PowDriverConsentLogMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 司机同意记录
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteDriverConsentLogServiceImpl implements RemoteDriverConsentLogService {
    private final PowDriverConsentLogMapper consentLogMapper;
    private final PowDriverMapper baseMapper;
    private final PowAgentMapper agentMapper;

    @Override
    public boolean insertDriverConsentLog(Long driverId) {
        PowDriver powDriver = baseMapper.selectById(driverId);
        if (powDriver == null){
            return false;
        }
        PowAgent powAgent = agentMapper.selectById(powDriver.getAgentId());
        PowDriverConsentLog log = new PowDriverConsentLog();
        log.setTenantId("000000");
        log.setScene(DriverConsentSceneEnum.WITHDRAW.getCode());
        log.setAgentId(powDriver.getAgentId());
        log.setAgentName(powAgent == null ? null : powAgent.getCompanyName());
        log.setDriverId(powDriver.getId());
        log.setPhone(powDriver.getPhone());
        log.setName("《钱包提现协议》");
        log.setIp(ServletUtils.getClientIP());
        log.setConsentTime(new Date());
        return consentLogMapper.insert(log) > 0;
    }
}
