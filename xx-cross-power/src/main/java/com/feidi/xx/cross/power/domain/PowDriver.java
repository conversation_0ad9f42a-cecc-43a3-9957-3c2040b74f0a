package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 司机对象 pow_driver
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_driver")
public class PowDriver extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分组
     */
    private Long groupId;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 上级
     */
    private Long parentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private String sex;

    /**
     * 资产密码
     */
    private String capitalPassword;

    /**
     * 司机类型
     */
    private String type;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 准驾车型
     */
    private String approvedType;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否接单
     */
    private String receive;

    /**
     * 状态
     */
    private String status;

    /**
     * 身份
     */
    private String identity;

    /**
     * 是否合规
     */
    private String legal;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 订单转卖服务费比例
     */
    private BigDecimal resellServiceRate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
