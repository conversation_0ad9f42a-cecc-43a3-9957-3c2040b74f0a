package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentRateBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentRateVo;
import com.feidi.xx.cross.power.service.IPowAgentRateService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商 - 代理商佣金比例
 * 前端访问路由地址为:/power/agt/agent/rate
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/agent/rate")
public class AgtAgentRateController extends BaseController {

    private final IPowAgentRateService powAgentRateService;

    /**
     * 获取代理商佣金比例详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowAgentRateVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powAgentRateService.queryById(id));
    }

    /**
     * 新增代理商佣金比例
     */
    @Log(title = "代理商佣金比例", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentRateBo bo) {
        return toAjax(powAgentRateService.insertByBo(bo));
    }
}
