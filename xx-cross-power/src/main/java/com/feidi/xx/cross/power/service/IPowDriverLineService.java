package com.feidi.xx.cross.power.service;


import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverLineForm;

import java.util.List;

/**
 * 司机线路Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface IPowDriverLineService {

    /**
     * 查询司机的线路列表
     */
    List<Long> listByDriverId(Long driverId);

    /**
     * 查询线路的司机列表
     */
    List<Long> listByLineId(Long lineId);

    /**
     *  给司机分配线路
     */
    boolean assignLine(PowDriverLineForm bo);

    /**
     *  给线路分配司机
     */
    boolean assignDriver(PowDriverLineForm bo);

    /**
     * 判断司机是否拥有线路
     * @param driverId
     * @param lineId
     * @return
     */
    Boolean driverHasLine(Long driverId, Long lineId);

}
