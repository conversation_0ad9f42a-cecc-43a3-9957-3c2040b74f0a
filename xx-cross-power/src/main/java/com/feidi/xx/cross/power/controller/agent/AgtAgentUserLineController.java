package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowAgentUserLineForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.service.IPowAgentUserLineService;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 代理商用户线路
 * 前端访问路由地址为:/power/agt/dispatch/line
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/dispatch/line")
public class AgtAgentUserLineController extends BaseController {

    private final IPowAgentUserService powAgentUserService;

    private final IPowAgentUserLineService powAgentUserLineService;

    /**
     * 线路管理-分配调度 查询代理商下调度用户-全部
     */
    @GetMapping("/list/all")
    public R<List<PowAgentUserVo>> list(PowAgentUserBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setStatus(StatusEnum.ENABLE.getCode());
        bo.setRole(AgentRoleType.DISPATCH.getCode());
        return R.ok(powAgentUserService.queryList(bo));
    }

    /**
     * 线路管理-给调度用户分配线路
     */
    @Log(title = "线路管理-给调度用户分配线路", businessType = BusinessType.UPDATE)
    @PostMapping("/assignAgentUser")
    public R<Void> assignAgentUser(@Validated @RequestBody PowAgentUserLineForm bo) {
        return toAjax(powAgentUserLineService.assignAgentUser(bo));
    }

    /**
     * 调度用户已分配的线路
     */
    @GetMapping("/{agentUserId}")
    public R<List<Long>> getDispatchUsersLine(@NotNull(message = "用户ID不能为空") @PathVariable("agentUserId") Long agentUserId) {
        return R.ok(powAgentUserLineService.listByAgentUserId(agentUserId));
    }
}
