package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.bo.MobiStatisticBo;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.vo.MobiNewNumVo;
import com.feidi.xx.cross.power.domain.vo.MobiStatisticVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;

import java.util.List;

/**
 * 后台 - 司机审核
 */
public interface IPowDriverReview {

    /**
     * 查询司机审核列表-分页
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PowDriverVo> queryPageList(PowDriverBo bo, PageQuery pageQuery);

    /**
     * 查询司机审核列表
     * @param bo
     * @return
     */
    List<PowDriverVo> queryList(PowDriverBo bo);

    /**
     * 获取司机审核详情
     * @param id
     * @return
     */
    PowDriverVo reviewDetail(Long id);

    /**
     * 司机审核
     */
    Boolean review(PowDriverBo bo);

    /**
     * 移动端 运营数据看板
     * @param bo bo
     * @return MobiStatisticVo
     */
    MobiStatisticVo queryStatistic(MobiStatisticBo bo);

    /**
     * 移动端 运营 联合查询
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PowDriverVo> queryPageList2(PowDriverBo bo, PageQuery pageQuery);

    MobiNewNumVo newNum(PowDriverBo bo);
}
