package com.feidi.xx.cross.power.domain.pojo.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 司机位置信息
 * <AUTHOR>
 */
@Data
public class PowDriverPositionForm implements Serializable {

    /**
     * 线路ids
     */
    private List<Long> lineIds;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;
    /**
     * 所属代理商
     */
    private Long agentId;
    /**
     * 司机id
     */
    private Long driverId;
}
