package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowAgentUserLineForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.service.IPowAgentUserLineService;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 调度管理
 * 前端访问路由地址为:/power/agt/dispatch
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/dispatch")
public class AgtDispatchController extends BaseController {


    private final IPowAgentUserService powAgentUserService;

    private final IPowAgentUserLineService powAgentUserLineService;

    /**
     * 查询代理商调度用户列表
     */
    @GetMapping("/list")
    public TableDataInfo<PowAgentUserVo> list(PowAgentUserBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        return powAgentUserService.queryDispatchPageList(bo, pageQuery);
    }

    /**
     * 新增代理商用户
     */
    @Log(title = "新增代理商用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentUserBo bo) {
        bo.setIsHandlePwd(true);
        bo.setAgentId(LoginHelper.getAgentId());
        return toAjax(powAgentUserService.insertByBo(bo));
    }

    /**
     * 修改代理商用户
     */
    @Log(title = "修改代理商用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAgentUserBo bo) {
        bo.setIsHandlePwd(true);
        return toAjax(powAgentUserService.updateByBo(bo));
    }

    /**
     * 删除代理商用户
     *
     * @param ids 主键串
     */
    @Log(title = "删除代理商用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> deletePowerUserByIds(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(powAgentUserService.deletePowerUserByIds(List.of(ids)));
    }

    /**
     * 代理商重置调度/财务密码
     */
    @Log(title = "重置代理商用户密码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@Validated(EditGroup.class) @RequestBody PowAgentUserBo bo) {
        return toAjax(powAgentUserService.resetPassword(bo));
    }

    /**
     * 调度管理-线路已分配的调度用户
     */
    @GetMapping("/{lineId}")
    public R<List<Long>> getDispatchUsers(@NotNull(message = "线路不能为空") @PathVariable("lineId") Long lineId) {
        return R.ok(powAgentUserService.listByLineId(lineId));
    }

    /**
     * 调度管理-给线路分配调度用户
     */
    @Log(title = "调度管理-分配线路给调度用户", businessType = BusinessType.UPDATE)
    @PostMapping("/assignLine")
    public R<Void> assignLine(@Validated @RequestBody PowAgentUserLineForm bo) {
        return toAjax(powAgentUserLineService.assignLine(bo));
    }
}
