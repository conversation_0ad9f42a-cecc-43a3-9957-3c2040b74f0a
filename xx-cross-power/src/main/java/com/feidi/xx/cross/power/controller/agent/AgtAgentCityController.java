package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentCityVo;
import com.feidi.xx.cross.power.service.IPowAgentCityService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商 - 代理商城市
 * 前端访问路由地址为:/power/agt/agent/city
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/agent/city")
public class AgtAgentCityController extends BaseController {

    private final IPowAgentCityService powAgentCityService;


    /**
     * 获取代理商城市详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowAgentCityVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powAgentCityService.queryById(id));
    }

    /**
     * 新增代理商城市
     */
    @Log(title = "代理商城市", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentCityBo bo) {
        return toAjax(powAgentCityService.insertByBo(bo));
    }
}
