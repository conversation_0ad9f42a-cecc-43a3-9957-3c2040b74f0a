package com.feidi.xx.cross.power.domain.pojo.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 加盟身份信息试图对象
 */
@Data
public class PowDriverIdCardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 脱敏姓名
     */
    private String desensitizedName;

    /**
     * 脱敏身份证号
     */
    private String desensitizedCarNo;


    /**
     * 姓名
     */
    private String name;


    /**
     * 身份证号
     */
    private String carNo;

    /**
     * 出生年月
     */
    private String birthday;

    /**
     * 性别[SexEnum]
     */
    private String sex;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 身份证(正面)
     */
    private Long frontOssId;

    /**
     * 身份证(反面)
     */
    private Long backOssId;

    private Long authOssId;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 身份证正面url
     */
    private String frontImg;

    /**
     * 身份证反面url
     */
    private String backImg;

    private String authImg;
}
