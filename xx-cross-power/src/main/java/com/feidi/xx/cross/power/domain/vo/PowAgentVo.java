package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.cross.power.domain.PowAgent;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 代理商列表视图对象 pow_agent
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAgent.class)
public class PowAgentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 上级代理商ID
     */
    @ExcelProperty(value = "上级代理商ID")
    private Long parentId;

    /**
     * 上级代理名称
     */
    @ExcelProperty(value = "上级代理名称")
    private String parentAgentName;

    /**
     * 营业执照
     */
    @ExcelProperty(value = "营业执照")
    private String license;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String companyName;

    /**
     * 税号[组织代码]
     */
    @ExcelProperty(value = "税号[组织代码]")
    private String taxNo;

    /**
     * 技术服务费比例
     */
    @ExcelProperty(value = "技术服务费比例")
    private BigDecimal technicalFeeRatio;
    /**
     * 客服电话
     */
    @ExcelProperty(value = "客服电话")
    private String servicesPhone;
    /**
     * 法人
     */
    @ExcelProperty(value = "法人")
    private String legalPerson;

    /**
     * 法人手机号
     */
    private String legalPhone;

    /**
     * 法人身份证号
     */
    @ExcelProperty(value = "法人身份证号")
    private String carNo;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 支行
     */
    private String subBank;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    @Id2Name(fullName="province",index = "id")
    private Long provinceId;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private Long cityId;

    @Id2Name(fullName="city",index = "cityCode")
    private String cityCode;
    /**
     * 区
     */
    @ExcelProperty(value = "区")
    @Id2Name(fullName="district",index = "id")
    private Long districtId;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 保证金
     */
    private Long earnest;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;
    private String statusText;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 信用代码
     */
    @ExcelProperty(value = "信用代码")
    private String creditCode;

    /**
     * 联系人姓名
     */
    @ExcelProperty(value = "联系人姓名")
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 自动抢单状态
     */
    @ExcelProperty(value = "自动抢单状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isAutoRob;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 招商地址
     */
    @ExcelProperty(value = "招商地址")
    private String inviteUrl;

    /**
     * 招商广告
     */
    @ExcelProperty(value = "招商广告")
    private String inviteImage;

    /**
     * 子代理商
     */
    private List<PowAgentVo> children;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分佣比例
     */
    private BigDecimal rate;

    /**
     * 分佣比例
     */
    private List<PowAgentRateVo> rates;

    /**
     * 运营城市
     */
    private List<String> cityNames;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否展示运营城市的全部订单[IsYesEnum]
     */
    private String showCityOrder;

    /**
     * 开启虚拟电话[IsYesEnum]
     */
    private String axb;

    /**
     * 使用外省虚拟电话[IsYesEnum]
     */
    private String useAllAxb;
}
