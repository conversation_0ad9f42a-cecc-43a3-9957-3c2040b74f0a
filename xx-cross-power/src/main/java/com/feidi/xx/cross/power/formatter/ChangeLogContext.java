package com.feidi.xx.cross.power.formatter;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 变更日志上下文
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@Builder
public class ChangeLogContext {

    /**
     * 主体对象（如司机姓名、用户名等）
     */
    private Object main;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段显示名称（中文名称）
     */
    private String fieldDisplayName;

    /**
     * 旧值
     */
    private Object oldValue;

    /**
     * 新值
     */
    private Object newValue;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 额外的上下文数据
     */
    @Builder.Default
    private Map<String, Object> extraData = new HashMap<>();

    /**
     * 添加额外数据
     *
     * @param key   键
     * @param value 值
     * @return 当前上下文对象
     */
    public ChangeLogContext addExtraData(String key, Object value) {
        this.extraData.put(key, value);
        return this;
    }

    /**
     * 获取所有可用的变量（用于SpEL表达式）
     *
     * @return 变量映射
     */
    public Map<String, Object> getAllVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("main", main);
        variables.put("fieldName", fieldName);
        variables.put("fieldDisplayName", fieldDisplayName);
        variables.put("oldValue", oldValue);
        variables.put("newValue", newValue);
        variables.put("operator", operator);
        variables.putAll(extraData);
        return variables;
    }
}
