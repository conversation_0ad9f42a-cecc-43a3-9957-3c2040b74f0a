package com.feidi.xx.cross.power.formatter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单的变更日志格式化器
 * 支持基本的占位符替换和SpEL表达式
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class SimpleChangeLogFormatter implements ChangeLogFormatter {

    private final ExpressionParser parser = new SpelExpressionParser();
    private final Map<String, Expression> expressionCache = new ConcurrentHashMap<>();

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 占位符模式：{expression}
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    @Override
    public String format(String template, ChangeLogContext context) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }

        try {
            return processTemplate(template, context);
        } catch (Exception e) {
            log.error("格式化变更日志失败: {}", template, e);
            return template; // 降级处理
        }
    }

    /**
     * 处理模板字符串
     */
    private String processTemplate(String template, ChangeLogContext context) {
        String result = template;
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);

        while (matcher.find()) {
            String placeholder = matcher.group(0); // {expression}
            String expression = matcher.group(1);  // expression

            try {
                String value = evaluateExpression(expression, context);
                result = result.replace(placeholder, value);
            } catch (Exception e) {
                log.warn("解析表达式失败: {}", expression, e);
                // 保留原始占位符
            }
        }

        return result;
    }

    /**
     * 评估表达式
     */
    private String evaluateExpression(String expression, ChangeLogContext context) {
        // 先尝试简单变量替换
        Object simpleValue = getSimpleVariable(expression, context);
        if (simpleValue != null) {
            return simpleValue.toString();
        }

        // 再尝试SpEL表达式
        try {
            Expression spelExpression = getExpression(expression);
            EvaluationContext evaluationContext = createEvaluationContext(context);
            Object result = spelExpression.getValue(evaluationContext);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.debug("SpEL表达式执行失败: {}", expression, e);
            return "";
        }
    }

    /**
     * 获取简单变量值
     */
    private Object getSimpleVariable(String varName, ChangeLogContext context) {
        return switch (varName) {
            case "main" -> context.getMain();
            case "fieldName" -> context.getFieldName();
            case "fieldDisplayName" -> context.getFieldDisplayName();
            case "oldValue" -> context.getOldValue();
            case "newValue" -> context.getNewValue();
            case "operator" -> context.getOperator();
            default -> context.getExtraData().get(varName);
        };
    }

    /**
     * 创建SpEL评估上下文
     */
    private EvaluationContext createEvaluationContext(ChangeLogContext context) {
        StandardEvaluationContext evaluationContext = new StandardEvaluationContext();

        // 设置变量
        evaluationContext.setVariable("main", context.getMain());
        evaluationContext.setVariable("fieldName", context.getFieldName());
        evaluationContext.setVariable("fieldDisplayName", context.getFieldDisplayName());
        evaluationContext.setVariable("oldValue", context.getOldValue());
        evaluationContext.setVariable("newValue", context.getNewValue());
        evaluationContext.setVariable("operator", context.getOperator());

        // 添加额外数据
        context.getExtraData().forEach(evaluationContext::setVariable);

        // 从Spring容器中加载格式化函数
        loadSpringFunctions(evaluationContext);

        return evaluationContext;
    }

    /**
     * 从Spring容器中加载格式化函数
     */
    private void loadSpringFunctions(StandardEvaluationContext evaluationContext) {
        try {
            // 获取所有实现了ChangeLogFunction接口的Bean
            Map<String, ChangeLogFunction> functionBeans = applicationContext.getBeansOfType(ChangeLogFunction.class);

            functionBeans.forEach((beanName, function) -> {
                String functionName = function.getFunctionName();
                if (functionName != null && !functionName.trim().isEmpty()) {
                    evaluationContext.setVariable(functionName, function);
                    log.debug("注册格式化函数: {} -> {}", functionName, beanName);
                }
            });
        } catch (Exception e) {
            log.warn("加载Spring格式化函数失败", e);
        }
    }

    /**
     * 获取表达式对象（带缓存）
     */
    private Expression getExpression(String expressionString) {
        return expressionCache.computeIfAbsent(expressionString, parser::parseExpression);
    }

}
