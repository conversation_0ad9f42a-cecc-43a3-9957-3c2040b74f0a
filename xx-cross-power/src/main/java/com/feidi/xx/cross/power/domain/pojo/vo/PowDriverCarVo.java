package com.feidi.xx.cross.power.domain.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 加盟车辆信息视图对象
 */
@Data
@Accessors(chain = true)
public class PowDriverCarVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车牌号
     */
    private String desensitizedLicensePlate;

    /**
     * 车辆品牌
     */
    private String desensitizedCarBrand;

    /**
     * 车辆型号
     */
    private String desensitizedCarModel;

    /**
     * 车辆颜色
     */
    private String desensitizedCarColor;

    /**
     * 车辆所有人
     */
    private String carOwner;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆品牌
     */
    private String carBrand;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆颜色
     */
    private String carColor;

    /**
     * 能源类型[UpEnergyTypeEnum]
     */
    private String driveType;

    /**
     * 核载人数
     */
    private Long seat;

    /**
     * 注册日期
     */
    private Date firstTime;

    /**
     * 发证日期
     */
    private Date startTime;

    /**+
     * 类型
     */
    private String carStyle;


    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 发动机代号
     */
    private String engine;

    /**
     * 车辆照片
     */
    private String car;

    /**
     * 行驶证(正页)
     */
    private Long travelFrontOssId;

    /**
     * 行驶证(副页)
     */
    private Long travelBackOssId;

    /**
     * 车辆保单
     */
    private Long insuranceOssId;

    /**
     * 车辆照片
     */
    private Long carOssId;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 车辆图片
     */
    private String carPic;

    /**
     * 行驶证正页url
     */
    private String licenseFrontImg;

    /**
     * 行驶证副页url
     */
    private String licenseBackImg;


    /**
     * 保单图片url
     */
    private String policyImg;
}
