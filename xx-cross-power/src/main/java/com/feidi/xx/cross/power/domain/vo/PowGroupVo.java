package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.power.domain.PowGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 司机组视图对象 pow_group
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowGroup.class)
public class PowGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商主体名称
     */
    @ExcelProperty(value = "代理商主体名称")
    private String mainBody;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ExcelProperty(value = "编码")
    private String code;

    /**
     * 佣金比例
     */
    @ExcelProperty(value = "佣金比例")
    private BigDecimal rate;

    /**
     * 图标
     */
    @ExcelProperty(value = "图标")
    private String icon;

    /**
     * 类型[DriverGroupTypeEnum 0默认 1正常]
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long sort;

    /**
     * 是否同步[IsYesEnum]
     */
    private String sync;

    /**
     * 司机数
     */
    @ExcelProperty(value = "司机数")
    private Long num;

    /**
     * 创建时间
     */
    private Date createTime;

}
