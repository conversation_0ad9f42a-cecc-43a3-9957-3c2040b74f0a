package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowGroupBo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.service.IPowGroupService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 司机组
 * 前端访问路由地址为:/power/agt/group
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/group")
public class AgtGroupController extends BaseController {

    private final IPowGroupService groupService;

    /**
     * 查询司机组列表-分页
     */
    @GetMapping("/list")
    public TableDataInfo<PowGroupVo> list(PowGroupBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        return groupService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询司机组列表-全部
     *
     * @param bo
     * @return
     */
    @GetMapping("/list/all")
    public R<List<PowGroupVo>> list(PowGroupBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        return R.ok(groupService.queryList(bo,PowGroupVo.class));
    }

    /**
     * 获取司机组详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowGroupVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(groupService.queryById(id));
    }

    /**
     * 新增司机组
     */
    @Log(title = "司机组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowGroupBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        return toAjax(groupService.insertByBo(bo));
    }

    /**
     * 修改司机组
     */
    @Log(title = "司机组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowGroupBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        return toAjax(groupService.updateByBo(bo));
    }

    /**
     * 删除司机组
     *
     * @param ids 主键串
     */
    @Log(title = "司机组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(groupService.deleteWithValidByIds(List.of(ids), true));
    }
}
