package com.feidi.xx.cross.power.controller.agent;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteAgentLineBo;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.service.IPowAgentService;
import com.feidi.xx.cross.power.service.IPowDriverService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 代理商列表 - 用于子代理商(V2)
 * 前端访问路由地址为:/power//agt/agent
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/agent")
public class AgtAgentController extends BaseController {

    private final IPowAgentService powAgentService;

    private final IPowDriverService powDriverService;

    /**
     * 查询代理商列表列表
     */
    @GetMapping("/list")
    @Id2NameAspect
    @Enum2TextAspect
    public TableDataInfo<PowAgentVo> list(PowAgentBo bo, PageQuery pageQuery) {
        bo.setParentId(LoginHelper.getAgentId());
        return powAgentService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询代理商列表 - 不分页
     */
    @GetMapping("/list/all")
    public R<List<PowAgentVo>> allList(PowAgentBo bo) {
        bo.setParentId(LoginHelper.getAgentId());
        return R.ok(powAgentService.queryAllList(bo));
    }

    /**
     * 导出代理商列表列表
     */
    @Log(title = "代理商列表", businessType = BusinessType.EXPORT)
    @Download(name="代理商列表",module = ModuleConstants.POWER)
    @PostMapping("/export")
    public void export(PowAgentBo bo, HttpServletResponse response) {
        List<PowAgentVo> list = powAgentService.queryList(bo);
        ExcelUtil.exportExcel(list, "代理商列表", PowAgentVo.class, response);
    }

    /**
     * 获取代理商列表详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowAgentVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        PowAgentVo vo = powAgentService.queryById(id);
        Assert.notNull(vo, "代理商不存在");
        Assert.isTrue(vo.getParentId().equals(LoginHelper.getAgentId()), "无权操作");
        return R.ok(powAgentService.queryById(id));
    }

    /**
     * 新增代理商列表
     */
    @Log(title = "代理商列表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentBo bo) {
        bo.setParentId(LoginHelper.getAgentId());
        return toAjax(powAgentService.insertByBo(bo));
    }

    /**
     * 修改代理商列表
     */
    @Log(title = "代理商列表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAgentBo bo) {
        bo.setParentId(LoginHelper.getAgentId());
        PowAgentVo vo = powAgentService.queryById(bo.getId());
        Assert.notNull(vo, "代理商不存在");
        Assert.isTrue(vo.getParentId().equals(LoginHelper.getAgentId()), "无权操作");
        return toAjax(powAgentService.updateByBo(bo));
    }

    /**
     * 代理商 状态 开关
     *
     *  禁用场景：
     *  TODO 验证是否：
     *      有二级代理商（禁用|线路清空|司机自动抢单禁用）、
     *      有司机（司机必须禁用|司机自动抢单禁用）、
     *      有线路（清空|下面的二级代理商线路清空|司机线路清空）、
     *      有自动抢单（禁用）
     *
     *  参数加一个状态
     */
    @Log(title = "代理商", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> editStatus(@PathVariable Long id, @PathVariable String status) {
        PowAgentVo vo = powAgentService.queryById(id);
        Assert.notNull(vo, "代理商不存在");
        Assert.isTrue(vo.getParentId().equals(LoginHelper.getAgentId()), "无权操作");
        return toAjax(powAgentService.updateStatus(id, status));
    }

    /**
     * 删除代理商
     *
     *  TODO 验证是否：
     *      有二级代理商（禁用|线路清空|司机自动抢单禁用）、
     *      有司机（司机必须禁用|司机自动抢单禁用）、
     *      有线路（清空|下面的二级代理商线路清空|司机线路清空）、
     *      有自动抢单（禁用）
     *
     * @param ids 主键串
     */
    @Log(title = "代理商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        List<PowAgentVo> childVos = powAgentService.queryChildrenByAgentId(LoginHelper.getAgentId());
        childVos.stream().map(PowAgentVo::getId).toList();
        return toAjax(powAgentService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取代理线路
     */
    @RepeatSubmit()
    @GetMapping("/lines/{id}")
    public R<List<Long>> getLines(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        PowAgentVo vo = powAgentService.queryById(id);
        Assert.notNull(vo, "代理商不存在");
        Assert.isTrue(vo.getParentId().equals(LoginHelper.getAgentId()), "无权操作");
        return R.ok(powAgentService.listByAgentId(id));
    }

    /**
     *  给二级代理商分配线路
     * @return
     */
    @Log(title = "代理商", businessType = BusinessType.UPDATE)
    @PostMapping("/assignLine")
    public R<Void> assignLine(@Validated @RequestBody RemoteAgentLineBo bo) {
        bo.setParentId(LoginHelper.getAgentId());
        powAgentService.assignLine2child(bo);
        return R.ok();
    }

    /**
     * 司机邀请有奖二维码
     */
    @GetMapping("/invite/prize")
    public R<String> generateInvitePrizeCode() {
        Long userId = LoginHelper.getUserId();
        return R.ok("操作成功",powDriverService.generateInvitePrizeCode(userId));
    }
}
