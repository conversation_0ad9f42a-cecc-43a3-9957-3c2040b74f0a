package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 司机佣金比例业务对象 pow_driver_rate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriverRate.class, reverseConvertGenerate = false)
public class PowDriverRateBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 司机ID
     */
    @NotNull(message = "司机ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 佣金比例
     */
    @NotNull(message = "佣金比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal rate;


}
