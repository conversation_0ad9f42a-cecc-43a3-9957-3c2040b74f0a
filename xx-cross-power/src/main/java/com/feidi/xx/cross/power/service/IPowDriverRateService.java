package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowDriverRateVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverRateBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 司机佣金比例Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IPowDriverRateService {

    /**
     * 查询司机佣金比例
     *
     * @param id 主键
     * @return 司机佣金比例
     */
    PowDriverRateVo queryById(Long id);

    /**
     * 分页查询司机佣金比例列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机佣金比例分页列表
     */
    TableDataInfo<PowDriverRateVo> queryPageList(PowDriverRateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机佣金比例列表
     *
     * @param bo 查询条件
     * @return 司机佣金比例列表
     */
    List<PowDriverRateVo> queryList(PowDriverRateBo bo);

    /**
     * 新增司机佣金比例
     *
     * @param bo 司机佣金比例
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverRateBo bo);

    /**
     * 修改司机佣金比例
     *
     * @param bo 司机佣金比例
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverRateBo bo);

    /**
     * 校验并批量删除司机佣金比例信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
