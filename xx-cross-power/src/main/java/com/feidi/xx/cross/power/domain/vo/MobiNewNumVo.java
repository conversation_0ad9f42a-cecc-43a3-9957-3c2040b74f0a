package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor()
@EqualsAndHashCode(callSuper = true)
public class MobiNewNumVo extends BaseEntity {

    /**
     * 全部
     */
    private Long all;

    /**
     * 待审核
     */
    private Long auditing;

    /**
     * 已通过
     */
    private Long auditSuccess;

    /**
     * 已驳回
     */
    private Long auditReject;
}
