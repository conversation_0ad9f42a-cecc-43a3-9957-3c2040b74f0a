package com.feidi.xx.cross.power;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 运力模块
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXCrossPowerApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXCrossPowerApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  运力模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
