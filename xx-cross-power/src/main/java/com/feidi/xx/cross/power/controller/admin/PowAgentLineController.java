package com.feidi.xx.cross.power.controller.admin;

import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.cross.power.domain.vo.PowAgentLineVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentLineBo;
import com.feidi.xx.cross.power.service.IPowAgentLineService;

import java.util.List;

/**
 * 后台 - 代理商线路
 * 前端访问路由地址为:/power/agent/line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/agent/line")
public class PowAgentLineController extends BaseController {

    private final IPowAgentLineService powAgentLineService;

    /**
     * 获取代理商线路详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerAgentLine:query")
    @GetMapping("/{id}")
    public R<PowAgentLineVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powAgentLineService.queryById(id));
    }

    /**
     * 新增代理商线路
     */
    @SaCheckPermission("power:powerAgentLine:add")
    @Log(title = "代理商线路", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentLineBo bo) {
        return toAjax(powAgentLineService.insertByBo(bo));
    }
    /**
     * 获取代理线路
     */
    @RepeatSubmit()
    @SaCheckPermission("power:agent:query")
    @GetMapping("/lines/{id}")
    public R<List<Long>> getLines(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powAgentLineService.listByAgentId(id));
    }

}
