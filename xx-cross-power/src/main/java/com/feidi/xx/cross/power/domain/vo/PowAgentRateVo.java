package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.power.domain.PowAgentRate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 代理商佣金比例视图对象 pow_agent_rate
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAgentRate.class)
public class PowAgentRateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "平台编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PlatformEnum")
    private String platformCode;

    private String name;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;

    /**
     * 佣金比例
     */
    @ExcelProperty(value = "佣金比例")
    private BigDecimal rate;

    /**
     * 状态
     */
    private String status;
}
