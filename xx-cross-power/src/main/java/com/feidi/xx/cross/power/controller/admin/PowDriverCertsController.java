package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowDriverCertsVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverCertsBo;
import com.feidi.xx.cross.power.service.IPowDriverCertsService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 司机证件
 * 前端访问路由地址为:/power/driver/cert
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/cert")
public class PowDriverCertsController extends BaseController {

    private final IPowDriverCertsService powDriverCertsService;

    /**
     * 查询司机证件列表
     */
    @SaCheckPermission("power:powerDriverCerts:list")
    @GetMapping("/list")
    public TableDataInfo<PowDriverCertsVo> list(PowDriverCertsBo bo, PageQuery pageQuery) {
        return powDriverCertsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机证件列表
     */
    @SaCheckPermission("power:powerDriverCerts:export")
    @Log(title = "司机证件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverCertsBo bo, HttpServletResponse response) {
        List<PowDriverCertsVo> list = powDriverCertsService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机证件", PowDriverCertsVo.class, response);
    }

    /**
     * 获取司机证件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerDriverCerts:query")
    @GetMapping("/{id}")
    public R<PowDriverCertsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverCertsService.queryById(id));
    }

    /**
     * 新增司机证件
     */
    @SaCheckPermission("power:powerDriverCerts:add")
    @Log(title = "司机证件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverCertsBo bo) {
        return toAjax(powDriverCertsService.insertByBo(bo));
    }

    /**
     * 修改司机证件
     */
    @SaCheckPermission("power:powerDriverCerts:edit")
    @Log(title = "司机证件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverCertsBo bo) {
        return toAjax(powDriverCertsService.updateByBo(bo));
    }

    /**
     * 删除司机证件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerDriverCerts:remove")
    @Log(title = "司机证件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powDriverCertsService.deleteWithValidByIds(List.of(ids), true));
    }
}
