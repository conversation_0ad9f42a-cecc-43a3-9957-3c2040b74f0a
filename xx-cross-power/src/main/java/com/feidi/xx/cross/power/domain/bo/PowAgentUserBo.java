package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;

/**
 * 代理商用户业务对象 pow_agent_user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAgentUser.class, reverseConvertGenerate = false)
public class PowAgentUserBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色[管理员|调度|财务]
     */
    private String role;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    @Length(message = "备注内容不能超过50个字符限制", max = 50)
    private String remark;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否按传入的密码修改
     */
    private Boolean isHandlePwd = false;
}
