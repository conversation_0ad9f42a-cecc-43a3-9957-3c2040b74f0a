package com.feidi.xx.cross.power.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowAuditRecordBo;
import com.feidi.xx.cross.power.domain.vo.PowAuditRecordVo;
import com.feidi.xx.cross.power.domain.PowAuditRecord;
import com.feidi.xx.cross.power.mapper.PowAuditRecordMapper;
import com.feidi.xx.cross.power.service.IPowAuditRecordService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 审核记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAuditRecordServiceImpl implements IPowAuditRecordService {

    private final PowAuditRecordMapper baseMapper;

    /**
     * 查询审核记录
     *
     * @param id 主键
     * @return 审核记录
     */
    @Override
    public PowAuditRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询审核记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审核记录分页列表
     */
    @Override
    public TableDataInfo<PowAuditRecordVo> queryPageList(PowAuditRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAuditRecord> lqw = buildQueryWrapper(bo);
        Page<PowAuditRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的审核记录列表
     *
     * @param bo 查询条件
     * @return 审核记录列表
     */
    @Override
    public List<PowAuditRecordVo> queryList(PowAuditRecordBo bo) {
        LambdaQueryWrapper<PowAuditRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowAuditRecord> buildQueryWrapper(PowAuditRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowAuditRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowAuditRecord::getDriverId, bo.getDriverId());
        lqw.eq(bo.getAgentId() != null, PowAuditRecord::getAgentId, bo.getAgentId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PowAuditRecord::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditUser()), PowAuditRecord::getAuditUser, bo.getAuditUser());
        return lqw;
    }

    /**
     * 新增审核记录
     *
     * @param bo 审核记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowAuditRecordBo bo) {
        PowAuditRecord add = MapstructUtils.convert(bo, PowAuditRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改审核记录
     *
     * @param bo 审核记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowAuditRecordBo bo) {
        PowAuditRecord update = MapstructUtils.convert(bo, PowAuditRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAuditRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除审核记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
