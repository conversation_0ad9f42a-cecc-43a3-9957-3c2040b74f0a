package com.feidi.xx.cross.power.controller.driver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.power.service.IPowDriverLineService;
import com.feidi.xx.system.api.RemoteDistrictService;
import com.feidi.xx.system.api.domain.vo.RemoteDistrictNameVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 司机端 - 线路管理
 * 前端访问路由地址为:/power/drv/line
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/line")
public class DrvLineController extends BaseController {

    private final IPowDriverLineService driverLineService;
    @DubboReference
    private final RemoteDistrictService remoteDistrictService;
    @DubboReference
    private final RemoteLineService remoteLineService;

    /**
     * 获取线路信息
     */
    @GetMapping()
    public R<Map<String, List<String>>> queryLines(StartEndEnum startEnd) {
        List<Long> lineIds = driverLineService.listByDriverId(LoginHelper.getUserId());
        if (CollUtil.isNotEmpty(lineIds)) {
            List<RemoteLineVo> lineVos = remoteLineService.queryByLineIds(lineIds);
            Set<Long> cityIds = new HashSet<>();
            List<Long> startCityIds = lineVos.stream().map(RemoteLineVo::getStartCityId).toList();
            List<Long> endCityIds = lineVos.stream().map(RemoteLineVo::getEndCityId).toList();
            if (Objects.equals(startEnd, StartEndEnum.START)) {
                cityIds.addAll(startCityIds);
            } else if (Objects.equals(startEnd, StartEndEnum.END)) {
                cityIds.addAll(endCityIds);
            } else {
                cityIds.addAll(startCityIds);
                cityIds.addAll(endCityIds);
            }
            List<RemoteDistrictNameVo> districtVos = remoteDistrictService.queryByIds(cityIds);
            Map<String, List<String>> map = districtVos.stream().map(RemoteDistrictNameVo::getName).collect(Collectors.groupingBy(e -> StrUtil.upperFirst(PinyinUtil.getPinyin(e).substring(0, 1))));
            return R.ok(map);
        }
        return R.ok();
    }

}
