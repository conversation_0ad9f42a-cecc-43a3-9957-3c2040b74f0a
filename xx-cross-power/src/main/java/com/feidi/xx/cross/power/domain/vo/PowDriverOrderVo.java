package com.feidi.xx.cross.power.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 司机订单信息
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
public class PowDriverOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 是否客诉[IsYesEnum]
     */
    private String complain;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 省
     */
    private String startProvince;

    /**
     * 地市
     */
    private String startCity;

    /**
     * 区
     */
    private String startDistrict;

    /**
     * 详细地址
     */
    private String startAddr;

    /**
     * 省
     */
    private String endProvince;

    /**
     * 地市
     */
    private String endCity;

    /**
     * 区
     */
    private String endDistrict;

    /**
     * 详细地址
     */
    private String endAddr;
}
