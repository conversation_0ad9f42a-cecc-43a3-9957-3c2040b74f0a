package com.feidi.xx.cross.power.domain.pojo.vo;

import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowAgent;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 代理商业务对象 Pow_agent
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@AutoMapper(target = PowAgent.class, reverseConvertGenerate = false)
public class PowPersonalForm {

    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空", groups = { EditGroup.class })
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = { EditGroup.class })
    private String phone;

    /**
     * 密码
     */
    private String password;

}
