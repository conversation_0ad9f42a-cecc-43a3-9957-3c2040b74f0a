package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.bo.PowDriverChangeLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverChangeLogVo;

import java.util.List;

/**
 * 司机信息变更记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface IPowDriverChangeLogService {

    /**
     * 查询司机信息变更记录
     *
     * @param id 主键
     * @return 司机信息变更记录
     */
    PowDriverChangeLogVo queryById(Long id);

    /**
     * 分页查询司机信息变更记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机信息变更记录分页列表
     */
    TableDataInfo<PowDriverChangeLogVo> queryPageList(PowDriverChangeLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机信息变更记录列表
     *
     * @param bo 查询条件
     * @return 司机信息变更记录列表
     */
    List<PowDriverChangeLogVo> queryList(PowDriverChangeLogBo bo);


    /**
     * 创建记录
     *
     * @param old
     * @param newBo
     */
    void create(PowDriverBo old, PowDriverBo newBo ,String title);
}
