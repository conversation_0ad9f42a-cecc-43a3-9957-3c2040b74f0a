package com.feidi.xx.cross.power.formatter.functions;

import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.cross.power.formatter.ChangeLogFunction;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.cross.power.mapper.PowGroupMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class DriverFormatFunctions {

    @Autowired
    PowDriverMapper powDriverMapper;
    @Autowired
    PowGroupMapper powGroupMapper;

    @Bean
    public ChangeLogFunction driverNameFunc() {
        return new ChangeLogFunction() {

            @Override
            public String getFunctionName() {
                return "driverName";
            }

            @Override
            public String format(Object value) {
                if (value == null) {
                    return "";
                }
                PowDriver powDriver = powDriverMapper.selectById(Long.valueOf(value.toString()));
                if (powDriver == null) {
                    return "";
                }
                return powDriver.getName();
            }
        };
    }

    @Bean
    public ChangeLogFunction driverGroupFunc() {
        return new ChangeLogFunction() {
            @Override
            public String getFunctionName() {
                return "driverGroup";
            }

            @Override
            public String format(Object value) {
                if (value == null) {
                    return "";
                }
                PowGroup powGroup = powGroupMapper.selectById(Long.valueOf(value.toString()));
                if (powGroup == null) {
                    return "";
                }
                return powGroup.getName();
            }
        };
    }
}
