package com.feidi.xx.cross.power.dubbo;

import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteDriverAccountService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverAccountVo;
import com.feidi.xx.cross.power.domain.PowDriverAccount;
import com.feidi.xx.cross.power.mapper.PowDriverAccountMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 司机服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteDriverAccountServiceImpl implements RemoteDriverAccountService {

    private final PowDriverAccountMapper driverAccountMapper;

    /**
     * 获取司机账户详情
     * @param accountId 账户ID
     * @return
     */
    @Override
    public RemoteDriverAccountVo getDriverAccount(Long accountId) {
        PowDriverAccount powDriverAccount = driverAccountMapper.selectById(accountId);
        return BeanUtils.copyProperties(powDriverAccount, RemoteDriverAccountVo.class);
    }

    @Override
    public List<RemoteDriverAccountVo> getDriverAccounts(Collection<Long> driverIds) {
        List<PowDriverAccount> accounts = driverAccountMapper.listByDriverId(driverIds);
        return BeanUtils.copyToList(accounts, RemoteDriverAccountVo.class);
    }
}
