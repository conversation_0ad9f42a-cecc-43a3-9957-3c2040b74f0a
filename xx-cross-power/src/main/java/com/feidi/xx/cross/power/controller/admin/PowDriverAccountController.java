package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverAccountBo;
import com.feidi.xx.cross.power.service.IPowDriverAccountService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 司机账户
 * 前端访问路由地址为:/power/driver/account
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/account")
public class PowDriverAccountController extends BaseController {

    private final IPowDriverAccountService powDriverAccountService;

    /**
     * 查询司机账户列表
     */
    @SaCheckPermission("power:powerDriverAccount:list")
    @GetMapping("/list")
    public TableDataInfo<PowDriverAccountVo> list(PowDriverAccountBo bo, PageQuery pageQuery) {
        return powDriverAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机账户列表
     */
    @SaCheckPermission("power:powerDriverAccount:export")
    @Log(title = "司机账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverAccountBo bo, HttpServletResponse response) {
        List<PowDriverAccountVo> list = powDriverAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机账户", PowDriverAccountVo.class, response);
    }

    /**
     * 获取司机账户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerDriverAccount:query")
    @GetMapping("/{id}")
    public R<PowDriverAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverAccountService.queryById(id));
    }

    /**
     * 新增司机账户
     */
    @SaCheckPermission("power:powerDriverAccount:add")
    @Log(title = "司机账户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverAccountBo bo) {
        return toAjax(powDriverAccountService.insertByBo(bo));
    }

    /**
     * 修改司机账户
     */
    @SaCheckPermission("power:powerDriverAccount:edit")
    @Log(title = "司机账户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverAccountBo bo) {
        return toAjax(powDriverAccountService.updateByBo(bo));
    }

    /**
     * 删除司机账户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerDriverAccount:remove")
    @Log(title = "司机账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powDriverAccountService.deleteWithValidByIds(List.of(ids), true));
    }
}
