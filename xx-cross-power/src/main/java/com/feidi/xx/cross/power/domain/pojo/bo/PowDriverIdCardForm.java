package com.feidi.xx.cross.power.domain.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.validate.ReviewGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 司机申请身份信息业务对象
 * <AUTHOR>
 */
@Data
@AutoMapper(target = PowDriver.class, reverseConvertGenerate = false)
public class PowDriverIdCardForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {ReviewGroup.class})
    @Size(min = 2, max = 30, message = "姓名只能包含中英文，长度为2到30个字符", groups = {ReviewGroup.class})
    @Pattern(regexp = "[\\u4e00-\\u9fa5a-zA-Z]{2,30}", message = "姓名只能包含中英文，长度为2到30个字符", groups = {ReviewGroup.class})
    private String name;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = {ReviewGroup.class})
    @Pattern(regexp = "^\\d{15}$|^\\d{17}[0-9Xx]$", message = "身份证号格式不正确", groups = {ReviewGroup.class})
    private String carNo;

    /**
     * 出生年月
     */
    //@NotBlank(message = "出生年月不能为空", groups = {ReviewGroup.class})
    private String birthday;

    /**
     * 性别[SexEnum]
     */
    @NotBlank(message = "性别不能为空", groups = {ReviewGroup.class})
    private String sex;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "有效截止日期不能为空", groups = {ReviewGroup.class})
    private Date endTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 身份证(正面)
     */
    @NotNull(message = "身份证(正面)不能为空", groups = {ReviewGroup.class})
    private Long frontOssId;

    /**
     * 身份证(反面)
     */
    @NotNull(message = "身份证(反面)不能为空", groups = {ReviewGroup.class})
    private Long backOssId;

    /**
     * 认证图像
     */
    //@NotNull(message = "认证图像不能为空", groups = {ReviewGroup.class})
    private Long authOssId;

    /**
     * 驳回原因
     */
    private List<String> reasons;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 常驻城市
     */
    private String cityCode;

    /**
     * 加盟类型[DriverTypeEnum]
     */
    private String type;

}
