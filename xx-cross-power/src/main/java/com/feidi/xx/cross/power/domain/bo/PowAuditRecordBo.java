package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowAuditRecord;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 审核记录业务对象 pow_audit_record
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAuditRecord.class, reverseConvertGenerate = false)
public class PowAuditRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机
     */
    @NotNull(message = "司机不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 代理商
     */
    @NotNull(message = "代理商不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 审核状态 [AuditStatusEnum]
     */
    @NotBlank(message = "审核状态 [AuditStatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 审核人
     */
    @NotBlank(message = "审核人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String auditUser;

    /**
     * 备注
     */
    private String remark;


}
