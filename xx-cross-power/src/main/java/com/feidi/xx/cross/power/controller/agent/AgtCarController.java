package com.feidi.xx.cross.power.controller.agent;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.bo.PowCarBo;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.cross.power.service.IPowCarService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 代理商 - 车辆
 * 前端访问路由地址为:/power/agt/car
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/car")
public class AgtCarController extends BaseController {

    private final IPowCarService powCarService;

    private final PowDriverMapper driverMapper;

    private final PowAgentMapper agentMapper;

    /**
     * 查询车辆列表
     */
    @GetMapping("/list")
    public TableDataInfo<PowCarVo> list(PowCarBo bo, PageQuery pageQuery) {
        Long agentId = LoginHelper.getAgentId();
        List<Long> agentIds = CollUtil.newArrayList(agentId);
        if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
            List<PowAgent> children = agentMapper.listByParentId(agentId);
            if (CollUtil.isNotEmpty(children)) {
                List<Long> childIds = StreamUtils.toList(children, PowAgent::getId);
                agentIds.addAll(childIds);
            }
        }

        List<PowDriver> drivers = driverMapper.listByAgentIds(agentIds);
        List<Long> driverIds = drivers.stream().map(PowDriver::getId).toList();
        bo.setDriverIds(driverIds);
        if (CollUtil.isEmpty(driverIds)) {
            return TableDataInfo.build();
        }
        TableDataInfo<PowCarVo> dataInfo = powCarService.queryPageList(bo, pageQuery);
        List<PowCarVo> carVos = dataInfo.getRows();
        fillInfo(carVos);
        return dataInfo;
    }

    private PowCarVo fillInfo(PowCarVo vo) {
        fillInfo(Collections.singletonList(vo));
        return vo;
    }

    private void fillInfo(List<PowCarVo> carVos) {
        if (CollUtil.isNotEmpty(carVos)) {
            List<Long> driverIds = carVos.stream().map(PowCarVo::getDriverId).toList();
            List<PowDriver> drivers = driverMapper.selectBatchIds(driverIds);
            Map<Long, PowDriver> driverMap = drivers.stream().collect(Collectors.toMap(PowDriver::getId, d -> d));
            for (PowCarVo carVo : carVos) {
                PowDriver powDriver = driverMap.get(carVo.getDriverId());
                if (powDriver != null) {
                    carVo.setDriverName(powDriver.getName());
                    carVo.setDriverPhone(powDriver.getPhone());
                }
            }
        }
    }

    /**
     * 导出车辆列表
     */
    @Log(title = "车辆", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowCarBo bo, HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        List<Long> agentIds = CollUtil.newArrayList(agentId);
        if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
            List<PowAgent> children = agentMapper.listByParentId(agentId);
            if (CollUtil.isNotEmpty(children)) {
                List<Long> childIds = StreamUtils.toList(children, PowAgent::getId);
                agentIds.addAll(childIds);
            }
        }

        List<PowDriver> drivers = driverMapper.listByAgentIds(agentIds);
        List<Long> driverIds = drivers.stream().map(PowDriver::getId).toList();
        if (CollUtil.isEmpty(driverIds)) {
            throw new ServiceException("当前无可导出的数据");
        }
        bo.setDriverIds(driverIds);
        List<PowCarVo> list = powCarService.queryList(bo);
        ExcelUtil.exportExcel(list, "车辆", PowCarVo.class, response);
    }

    /**
     * 获取车辆详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowCarVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        PowCarVo carVo = powCarService.queryById(id);
        return R.ok(fillInfo(carVo));
    }

    /**
     * 新增车辆
     */
    @Log(title = "车辆", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowCarBo bo) {
        return toAjax(powCarService.insertByBo(bo));
    }

    /**
     * 修改车辆
     */
    @Log(title = "车辆", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowCarBo bo) {
        return toAjax(powCarService.updateByBo(bo));
    }

    /**
     * 删除车辆
     *
     * @param ids 主键串
     */
    @Log(title = "车辆", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(powCarService.deleteWithValidByIds(List.of(ids), true));
    }
}
