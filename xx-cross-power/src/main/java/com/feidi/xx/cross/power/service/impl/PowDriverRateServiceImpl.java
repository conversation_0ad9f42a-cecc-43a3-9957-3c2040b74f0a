package com.feidi.xx.cross.power.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowDriverRateBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverRateVo;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.feidi.xx.cross.power.mapper.PowDriverRateMapper;
import com.feidi.xx.cross.power.service.IPowDriverRateService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 司机佣金比例Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class PowDriverRateServiceImpl implements IPowDriverRateService {

    private final PowDriverRateMapper baseMapper;

    /**
     * 查询司机佣金比例
     *
     * @param id 主键
     * @return 司机佣金比例
     */
    @Override
    public PowDriverRateVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机佣金比例列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机佣金比例分页列表
     */
    @Override
    public TableDataInfo<PowDriverRateVo> queryPageList(PowDriverRateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverRate> lqw = buildQueryWrapper(bo);
        Page<PowDriverRateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机佣金比例列表
     *
     * @param bo 查询条件
     * @return 司机佣金比例列表
     */
    @Override
    public List<PowDriverRateVo> queryList(PowDriverRateBo bo) {
        LambdaQueryWrapper<PowDriverRate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowDriverRate> buildQueryWrapper(PowDriverRateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), PowDriverRate::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getDriverId() != null, PowDriverRate::getDriverId, bo.getDriverId());
        lqw.eq(bo.getRate() != null, PowDriverRate::getRate, bo.getRate());
        return lqw;
    }

    /**
     * 新增司机佣金比例
     *
     * @param bo 司机佣金比例
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowDriverRateBo bo) {
        PowDriverRate add = MapstructUtils.convert(bo, PowDriverRate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机佣金比例
     *
     * @param bo 司机佣金比例
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowDriverRateBo bo) {
        PowDriverRate update = MapstructUtils.convert(bo, PowDriverRate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriverRate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除司机佣金比例信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
