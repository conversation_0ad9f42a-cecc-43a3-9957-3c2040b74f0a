package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverLineForm;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.service.IPowDriverLineService;
import com.feidi.xx.cross.power.service.IPowDriverService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 司机
 * 前端访问路由地址为:/power/agt/driver
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/driver")
public class AgtDriverController extends BaseController {

    private final IPowDriverService powDriverService;

    private final IPowDriverLineService powDriverLineService;

    /**
     * 查询司机列表
     */
    @Id2NameAspect
    @GetMapping("/list")
    public TableDataInfo<PowDriverVo> list(PowDriverBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        bo.setNeIdentity(DriverIdentityEnum.NO.getCode());
        return powDriverService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询司机列表-全部
     */
    @GetMapping("/list/all")
    public List<PowDriverVo> list(PowDriverBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        bo.setNeIdentity(DriverIdentityEnum.NO.getCode());
        return powDriverService.queryList(bo);
    }

    /**
     * 导出司机列表
     */
    @Log(title = "司机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverBo bo, HttpServletResponse response) {
        List<PowDriverVo> list = powDriverService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机", PowDriverVo.class, response);
    }

    /**
     * 获取司机详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowDriverVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverService.queryById(id));
    }

    /**
     * 修改司机状态 启用/禁用
     * @param id
     * @param status
     * @return
     */
    @Log(title = "代理商-修改司机状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> updateStatus(@PathVariable Long id, @PathVariable String status) {
        return toAjax(powDriverService.updateStatus(id, status));
    }

    /**
     * 新增司机
     */
    @Log(title = "司机", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverBo bo) {
        return toAjax(powDriverService.insertByBo(bo));
    }

    /**
     * 修改司机
     */
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverBo bo) {
        return toAjax(powDriverService.updateByBo(bo));
    }

    /**
     * 封禁司机
     *
     * @param id 主键串
     */
    @Log(title = "代理商-封禁司机", businessType = BusinessType.UPDATE)
    @PutMapping("/seal/{id}")
    public R<Void> seal(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(powDriverService.sealById(id, true));
    }

    /**
     * 解封司机
     *
     * @param id 主键串
     */
    @Log(title = "代理商-解封司机", businessType = BusinessType.UPDATE)
    @PutMapping("/unseal/{id}")
    public R<Void> unseal(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(powDriverService.unsealById(id));
    }

    /**
     * 分配线路给司机
     */
    @Log(title = "代理商-分配线路给司机", businessType = BusinessType.UPDATE)
    @PostMapping("/assignLine")
    public void assignLine(@Validated @RequestBody PowDriverLineForm bo) {
        powDriverLineService.assignLine(bo);
    }

    /**
     * 分配司机给线路
     */
    @Log(title = "代理商-分配司机给线路", businessType = BusinessType.UPDATE)
    @PostMapping("/assignDriver")
    public void assignDriver(@Validated @RequestBody PowDriverLineForm bo) {
        powDriverLineService.assignDriver(bo);
    }

    /**
     * 获取司机已分配的线路
     */
    @GetMapping("/lines/{id}")
    public R<List<Long>> getLines(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(powDriverLineService.listByDriverId(id));
    }

    /**
     * 获取线路已分配的司机
     */
    @GetMapping("/drivers/{id}")
    public R<List<Long>> getDrivers(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(powDriverLineService.listByLineId(id));
    }


    /**
     *  用于调度
     * @param bo
     * @return
     */
    @GetMapping("/dispatch")
    public TableDataInfo<PowDriverVo> dispatch(@Validated PowDriverBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setQueryPendingTripOrders(true);
        return powDriverService.dispatch(bo, pageQuery);
    }

    /**
     * 修改司机组
     */
    @RepeatSubmit()
    @Log(title = "代理商-修改司机组", businessType = BusinessType.UPDATE)
    @PostMapping("/editGroup")
    public R<Boolean> editGroup(@RequestBody PowDriverBo bo) {
        return R.ok(powDriverService.updateDriverGroup(bo));
    }
}
