package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteAgentLineBo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.service.IPowAgentService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 后台 - 代理商列表
 * 前端访问路由地址为:/power/agent
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/agent")
public class PowAgentController extends BaseController {

    private final IPowAgentService powAgentService;

    private final PowAgentMapper baseMapper;

    /**
     * 查询代理商列表列表
     */
    @SaCheckPermission("power:powerAgent:list")
    @GetMapping("/list")
    @Id2NameAspect
    public TableDataInfo<PowAgentVo> list(PowAgentBo bo, PageQuery pageQuery) {
        return powAgentService.queryPageList(bo, pageQuery);
    }
    /**
     * 查询代理商列表-全部
     * 父子关系
     */
    @SaCheckPermission("power:powerAgent:list")
    @GetMapping("/list/all")
    public R<List<PowAgentVo>> list(PowAgentBo bo) {
        bo.setParentId(0L);
        List<PowAgentVo> vos = powAgentService.queryAllList(bo);
        if (CollUtil.isNotEmpty(vos)) {
            List<Long> pids = StreamUtils.toList(vos, PowAgentVo::getId);
            List<PowAgent> parents = baseMapper.listByParentIds(pids);
            if (CollUtil.isNotEmpty(parents)) {
                List<PowAgentVo> convert = MapstructUtils.convert(parents, PowAgentVo.class);
                Map<Long, List<PowAgentVo>> parentMap = StreamUtils.groupByKey(convert, PowAgentVo::getParentId);
                for (PowAgentVo vo : vos) {
                    vo.setChildren(parentMap.get(vo.getId()));
                }
            }
        }
        return R.ok(vos);
    }

    /**
     * 导出代理商列表列表
     */
    @SaCheckPermission("power:powerAgent:export")
    @Log(title = "代理商列表", businessType = BusinessType.EXPORT)
    @Download(name="代理商列表",module = ModuleConstants.POWER)
    @PostMapping("/export")
    public void export(PowAgentBo bo, HttpServletResponse response) {
        List<PowAgentVo> list = powAgentService.queryList(bo);
        ExcelUtil.exportExcel(list, "代理商列表", PowAgentVo.class, response);
    }

    /**
     * 获取代理商列表详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerAgent:query")
    @GetMapping("/{id}")
    public R<PowAgentVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powAgentService.queryById(id));
    }

    /**
     * 新增代理商列表
     */
    @SaCheckPermission("power:powerAgent:add")
    @Log(title = "新增代理商", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentBo bo) {
        return toAjax(powAgentService.insertByBo(bo));
    }

    /**
     * 修改代理商列表
     */
    @SaCheckPermission("power:powerAgent:edit")
    @Log(title = "修改代理商", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAgentBo bo) {
        return toAjax(powAgentService.updateByBo(bo));
    }

    /**
     * 删除代理商列表
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerAgent:remove")
    @Log(title = "删除代理商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powAgentService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 代理商 状态 开关
     */
    @Log(title = "代理商列表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> editStatus(@PathVariable Long id, @PathVariable String status) {
        PowAgentVo vo = powAgentService.queryById(id);
        Assert.notNull(vo, "代理商不存在");
        return toAjax(powAgentService.updateStatus(id, status));
    }
    /**
     *  分配线路给代理
     */
    @RepeatSubmit()
    @SaCheckPermission("power:powerAgent:assignLine")
    @Log(title = "分配线路给代理商", businessType = BusinessType.UPDATE)
    @PostMapping("/assignLine")
    public R<Boolean> assignLine(@RequestBody @Validated RemoteAgentLineBo bo) {
        return R.ok(powAgentService.assignLine(bo));
    }
}
