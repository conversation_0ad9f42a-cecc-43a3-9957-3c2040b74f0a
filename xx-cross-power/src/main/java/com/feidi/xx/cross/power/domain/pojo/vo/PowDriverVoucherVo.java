package com.feidi.xx.cross.power.domain.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.LoginModeEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.power.domain.PowDriverVoucher;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机凭证视图对象 Pow_driver_voucher
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverVoucher.class)
public class PowDriverVoucherVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = LoginModeEnum.class)
    private String type;

    /**
     * app_id
     */
    @ExcelProperty(value = "app_id")
    private String appId;

    /**
     * open_id
     */
    @ExcelProperty(value = "open_id")
    private String openId;

    /**
     * union_id
     */
    @ExcelProperty(value = "union_id")
    private String unionId;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = StatusEnum.class)
    private String status;


}
