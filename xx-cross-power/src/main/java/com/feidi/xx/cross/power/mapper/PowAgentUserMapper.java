package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 代理商用户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowAgentUserMapper extends BaseMapperPlus<PowAgentUser, PowAgentUserVo> {

    /**
     * 根据邀请码
     */
    default PowAgentUser queryByCode(String code){
        return selectOne(new LambdaQueryWrapper<PowAgentUser>().eq(PowAgentUser::getInviteCode, code));
    }

    /**
     * 根据代理商id查询
     */
    default PowAgentUser queryByAgentId(Long agentId){
        return selectOne(new LambdaQueryWrapper<PowAgentUser>()
                .eq(PowAgentUser::getAgentId, agentId)
                .eq(PowAgentUser::getStatus, StatusEnum.ENABLE.getCode())
                .eq(PowAgentUser::getRole, AgentRoleType.ADMIN.getCode()));
    }
}
