package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 代理商佣金比例对象 pow_agent_rate
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_agent_rate")
public class PowAgentRate extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
