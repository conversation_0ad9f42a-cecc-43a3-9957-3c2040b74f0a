package com.feidi.xx.cross.power.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.cross.power.domain.PowDriverAccount;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;

/**
 * 司机账户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowDriverAccountMapper extends BaseMapperPlus<PowDriverAccount, PowDriverAccountVo> {
    /**
     * 根据司机id和账户类型查询司机账户信息
     */
    default PowDriverAccount selectByDriverIdAndType(Long driverId, String type) {
        return selectOne(new LambdaQueryWrapper<PowDriverAccount>()
                .eq(PowDriverAccount::getDriverId, driverId)
                .eq(PowDriverAccount::getType, type));
    }

    /**
     * 根据司机ids和账户类型查询司机账户信息
     */
    default List<PowDriverAccount> listByDriverId(Collection<Long> driverIds) {
        return selectList(new LambdaQueryWrapper<PowDriverAccount>()
                .in(CollUtil.isNotEmpty(driverIds), PowDriverAccount::getDriverId, driverIds));
    }

}
