package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.power.domain.PowDriverChangeLog;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 司机信息变更记录业务对象 pow_driver_change_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriverChangeLog.class, reverseConvertGenerate = false)
public class PowDriverChangeLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long driverId;

    /**
     * 分组id
     */
    @NotNull(message = "分组id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long agentId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 字段名称
     */
    @NotBlank(message = "字段名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String fieldName;

    /**
     * 老数据
     */
    @NotBlank(message = "老数据不能为空", groups = {AddGroup.class, EditGroup.class})
    private String oldValue;

    /**
     * 新数据
     */
    @NotBlank(message = "新数据不能为空", groups = {AddGroup.class, EditGroup.class})
    private String newValue;

    /**
     * 扩展
     */
    @NotBlank(message = "扩展不能为空", groups = {AddGroup.class, EditGroup.class})
    private String extra;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 操作名称
     */
    @NotBlank(message = "操作名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 展示信息
     */
    @NotBlank(message = "展示信息不能为空", groups = {AddGroup.class, EditGroup.class})
    private String msg;


    /**
     * 创建时间范围
     */
    private StartEndTime createTimeRange;

}
