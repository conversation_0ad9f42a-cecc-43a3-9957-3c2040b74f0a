package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.cross.power.domain.pojo.bo.PasswordForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代理商用户Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAgentUserService {

    /**
     * 查询代理商用户
     *
     * @param id 主键
     * @return 代理商用户
     */
    PowAgentUserVo queryById(Long id);

    /**
     * 根据手机号查询代理商信息
     * @param phone
     * @return
     */
    PowAgentUser queryByPhone(String phone);

    /**
     * 分页查询代理商用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商用户分页列表
     */
    TableDataInfo<PowAgentUserVo> queryPageList(PowAgentUserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代理商用户列表
     *
     * @param bo 查询条件
     * @return 代理商用户列表
     */
    List<PowAgentUserVo> queryList(PowAgentUserBo bo);

    /**
     * 新增代理商用户
     *
     * @param bo 代理商用户
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAgentUserBo bo);

    /**
     * 修改代理商用户
     *
     * @param bo 代理商用户
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAgentUserBo bo);

    /**
     * 校验并批量删除代理商用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验代理商用户是否存在
     *
     * @param code 代理商用户编码
     * @return 是否存在
     */
    Boolean exists(String code);

    PowAgentUser queryByAgentId(Long id);

    Boolean resetPwd(PasswordForm bo);

    PowAgentUserVo generateInviteCode();

    /**
     * 重置密码
     * @param bo
     * @return
     */
    Boolean resetPassword(PowAgentUserBo bo);

    Boolean deletePowerUserByIds(List<Long> ids);

    List<Long> listByLineId(Long lineId);

    /**
     * 调度管理列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PowAgentUserVo> queryDispatchPageList(PowAgentUserBo bo, PageQuery pageQuery);
}
