package com.feidi.xx.cross.power.domain.pojo.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 自营代扣试图对象
 * <AUTHOR>
 */
@Data
public class PowDriverAgreementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String status;
    /**
     * 代扣协议
     */
    private Long agreementOssId;

    /**
     * 手持身份证
     */
    private Long heldIdCardOssId;

    /**
     * 手持代扣协议
     */
    private Long heldAgreementOssId;

    /**
     * 代扣协议url
     */
    private String agreementImg;

    /**
     * 手持身份证url
     */
    private String heldIdCardImg;

    /**
     * 手持代扣协议url
     */
    private String heldAgreementImg;
}
