package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.cross.power.domain.PowAgentCity;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 代理商城市视图对象 pow_agent_city
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAgentCity.class)
public class PowAgentCityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;


    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String companyName;

    /**
     * 开城id
     */
    @ExcelProperty(value = "开城id")
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;
}
