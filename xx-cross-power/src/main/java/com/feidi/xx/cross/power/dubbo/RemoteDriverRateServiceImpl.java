package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteDriverRateService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverRateVo;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.feidi.xx.cross.power.mapper.PowDriverRateMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 司机佣金比例服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteDriverRateServiceImpl implements RemoteDriverRateService {

    private final PowDriverRateMapper baseMapper;
    @Override
    public RemoteDriverRateVo getDriverRateByDriverIdAndPlatformCode(Long driverId, String platformCode) {
        LambdaQueryWrapper<PowDriverRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowDriverRate::getDriverId, driverId)
                .eq(PowDriverRate::getPlatformCode, platformCode);
        PowDriverRate powDriverRate = baseMapper.selectOne(lqw);
        return BeanUtils.copyProperties(powDriverRate, RemoteDriverRateVo.class);
    }
}
