package com.feidi.xx.cross.power.domain.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.power.domain.PowDriverLine;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机线路视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverLine.class)
public class PowDriverLineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代理ID
     */
    @ExcelProperty(value = "代理ID")
    private Long agentId;

    /**
     * 司机ID
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 线路ID
     */
    @ExcelProperty(value = "线路ID")
    private Long lineId;

    /**
     * 主线路ID
     */
    private Long mainLineId;


}
