package com.feidi.xx.cross.power.domain.vo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.SourceEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverConsentSceneEnum;
import com.feidi.xx.cross.power.domain.PowDriverConsentLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机协议同意记录视图对象 pow_driver_consent_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverConsentLog.class)
public class PowDriverConsentLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private String agentName;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 业务场景
     */
    @ExcelProperty(value = "业务场景", converter = ExcelEnumConvert.class)
    @Enum2Text(enumClass = DriverConsentSceneEnum.class)
    @ExcelEnumFormat(enumClass = DriverConsentSceneEnum.class)
    private String scene;

    /**
     * 协议名称
     */
    @ExcelProperty(value = "协议名称")
    private String name;

    /**
     * ip
     */
    @ExcelProperty(value = "ip")
    private String ip;

    /**
     * 同意时间
     */
    @ExcelProperty(value = "同意时间")
    private Date consentTime;


}
