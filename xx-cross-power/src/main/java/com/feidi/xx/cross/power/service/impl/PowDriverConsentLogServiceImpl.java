package com.feidi.xx.cross.power.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.power.domain.PowDriverConsentLog;
import com.feidi.xx.cross.power.domain.bo.PowDriverConsentLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverConsentLogVo;
import com.feidi.xx.cross.power.mapper.PowDriverConsentLogMapper;
import com.feidi.xx.cross.power.service.IPowDriverConsentLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 司机协议同意记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RequiredArgsConstructor
@Service
public class PowDriverConsentLogServiceImpl implements IPowDriverConsentLogService {

    private final PowDriverConsentLogMapper baseMapper;

    /**
     * 查询司机协议同意记录
     *
     * @param id 主键
     * @return 司机协议同意记录
     */
    @Override
    public PowDriverConsentLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机协议同意记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机协议同意记录分页列表
     */
    @Override
    public TableDataInfo<PowDriverConsentLogVo> queryPageList(PowDriverConsentLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverConsentLog> lqw = buildQueryWrapper(bo);
        Page<PowDriverConsentLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机协议同意记录列表
     *
     * @param bo 查询条件
     * @return 司机协议同意记录列表
     */
    @Override
    public List<PowDriverConsentLogVo> queryList(PowDriverConsentLogBo bo) {
        LambdaQueryWrapper<PowDriverConsentLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowDriverConsentLog> buildQueryWrapper(PowDriverConsentLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverConsentLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowDriverConsentLog::getDriverId, bo.getDriverId());
        lqw.eq(bo.getAgentId() != null, PowDriverConsentLog::getAgentId, bo.getAgentId());
        lqw.eq(bo.getAgentName() != null, PowDriverConsentLog::getAgentName, bo.getAgentName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), PowDriverConsentLog::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getScene()), PowDriverConsentLog::getScene, bo.getScene());
        lqw.like(StringUtils.isNotBlank(bo.getName()), PowDriverConsentLog::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), PowDriverConsentLog::getIp, bo.getIp());
        lqw.eq(bo.getConsentTime() != null, PowDriverConsentLog::getConsentTime, bo.getConsentTime());
        lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), PowDriverConsentLog::getConsentTime, bo.getStartTime());
        lqw.le(StringUtils.isNotBlank(bo.getEndTime()), PowDriverConsentLog::getConsentTime, bo.getEndTime());
        lqw.orderByDesc(PowDriverConsentLog::getConsentTime);
        return lqw;
    }

    /**
     * 新增司机协议同意记录
     *
     * @param bo 司机协议同意记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowDriverConsentLogBo bo) {
        PowDriverConsentLog add = MapstructUtils.convert(bo, PowDriverConsentLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机协议同意记录
     *
     * @param bo 司机协议同意记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowDriverConsentLogBo bo) {
        PowDriverConsentLog update = MapstructUtils.convert(bo, PowDriverConsentLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriverConsentLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除司机协议同意记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
