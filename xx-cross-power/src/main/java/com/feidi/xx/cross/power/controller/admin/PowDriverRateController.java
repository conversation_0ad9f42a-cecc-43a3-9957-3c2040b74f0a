package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowDriverRateVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverRateBo;
import com.feidi.xx.cross.power.service.IPowDriverRateService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 司机佣金比例
 * 前端访问路由地址为:/power/driverRate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driverRate")
public class PowDriverRateController extends BaseController {

    private final IPowDriverRateService powDriverRateService;

    /**
     * 查询司机佣金比例列表
     */
    @SaCheckPermission("power:driverRate:list")
    @GetMapping("/list")
    public TableDataInfo<PowDriverRateVo> list(PowDriverRateBo bo, PageQuery pageQuery) {
        return powDriverRateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机佣金比例列表
     */
    @SaCheckPermission("power:driverRate:export")
    @Log(title = "司机佣金比例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverRateBo bo, HttpServletResponse response) {
        List<PowDriverRateVo> list = powDriverRateService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机佣金比例", PowDriverRateVo.class, response);
    }

    /**
     * 获取司机佣金比例详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:driverRate:query")
    @GetMapping("/{id}")
    public R<PowDriverRateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverRateService.queryById(id));
    }

    /**
     * 新增司机佣金比例
     */
    @SaCheckPermission("power:driverRate:add")
    @Log(title = "司机佣金比例", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverRateBo bo) {
        return toAjax(powDriverRateService.insertByBo(bo));
    }

    /**
     * 修改司机佣金比例
     */
    @SaCheckPermission("power:driverRate:edit")
    @Log(title = "司机佣金比例", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverRateBo bo) {
        return toAjax(powDriverRateService.updateByBo(bo));
    }

    /**
     * 删除司机佣金比例
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:driverRate:remove")
    @Log(title = "司机佣金比例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powDriverRateService.deleteWithValidByIds(List.of(ids), true));
    }
}
