package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteAgentUserService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentUserVo;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.cross.power.mapper.PowAgentUserMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 代理商服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteAgentUserImpl implements RemoteAgentUserService {

    private final PowAgentUserMapper baseMapper;
    @Override
    public RemoteAgentUserVo getAgentInfoByCode(String code) {
        LambdaQueryWrapper<PowAgentUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PowAgentUser::getInviteCode, code);
        PowAgentUser powAgentUser = baseMapper.selectOne(lqw);
        return BeanUtils.copyProperties(powAgentUser, RemoteAgentUserVo.class);
    }

    @Override
    public RemoteAgentUserVo getAgentInfoByAgentId(Long agentId) {
        PowAgentUser powAgentUser = baseMapper.queryByAgentId(agentId);
        return BeanUtils.copyProperties(powAgentUser, RemoteAgentUserVo.class);
    }

    /**
     * 代理商用户id查询代理商用户信息
     *
     * @param agentUserId 代理商用户id
     * @return 代理商用户信息
     */
    @Override
    public RemoteAgentUserVo getAgentUserInfoById(Long agentUserId) {
        PowAgentUser powAgentUser = baseMapper.selectById(agentUserId);
        return BeanUtils.copyProperties(powAgentUser, RemoteAgentUserVo.class);
    }
}
