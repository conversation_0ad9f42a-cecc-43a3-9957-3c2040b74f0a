package com.feidi.xx.cross.power.domain.pojo.bo;


import com.feidi.xx.cross.common.enums.finance.PwdVerifyEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.validate.PwdGroup;
import com.feidi.xx.cross.power.validate.SmsGroup;
import com.feidi.xx.cross.power.validate.VerifyGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 司机资产密码修改
 */
@Data
@AutoMapper(target = PowDriver.class, reverseConvertGenerate = false)
public class PowDriverCapitalPwdForm {

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空", groups = {PwdGroup.class, SmsGroup.class, VerifyGroup.class})
    private String capitalPassword;

    /**
     * 访问密钥
     */
    @NotBlank(message = "accessToken不能为空", groups = {PwdGroup.class})
    private String accessToken;

    /**
     * 短信验证码
     */
    //@NotBlank(message = "短信验证码不能为空", groups = {SmsGroup.class})
    private String smsCode;

    /**
     * 校验类型
     */
    @NotNull(message = "校验类型不能为空", groups = {VerifyGroup.class})
    private PwdVerifyEnum verifyType;

}
