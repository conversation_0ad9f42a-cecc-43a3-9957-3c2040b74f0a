package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.power.domain.PowAgentRate;
import com.feidi.xx.cross.power.domain.vo.PowAgentRateVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 代理商佣金比例Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowAgentRateMapper extends BaseMapperPlus<PowAgentRate, PowAgentRateVo> {

    /**
     * 根据代理id查询
     */
    default List<PowAgentRate> selectByAgentId(Long agentId) {
        return selectList(Wrappers.<PowAgentRate>lambdaQuery().eq(PowAgentRate::getAgentId, agentId));
    }

    /**
     * 根据司机id删除
     */
    default int deleteByAgentId(Long agentId) {
        return delete(Wrappers.<PowAgentRate>lambdaQuery().eq(PowAgentRate::getAgentId, agentId));
    }

    default List<PowAgentRate> listByPlatCodeAndAgentId(String platformCode, Long agentId) {
        return selectList(Wrappers.<PowAgentRate>lambdaQuery()
                .eq(ObjectUtils.isNotNull(platformCode), PowAgentRate::getPlatformCode, platformCode)
                .eq(ObjectUtils.isNotNull(agentId), PowAgentRate::getAgentId, agentId));
    }
}
