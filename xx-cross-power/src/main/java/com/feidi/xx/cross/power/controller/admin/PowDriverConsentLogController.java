package com.feidi.xx.cross.power.controller.admin;

import java.util.List;

import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.cross.power.domain.bo.PowDriverConsentLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverConsentLogVo;
import com.feidi.xx.cross.power.service.IPowDriverConsentLogService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 司机协议同意记录
 * 前端访问路由地址为:/settle/driverConsentLog
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driverConsentLog")
public class PowDriverConsentLogController extends BaseController {

    private final IPowDriverConsentLogService powDriverConsentLogService;

    /**
     * 查询司机协议同意记录列表
     */
    @SaCheckPermission("settle:driverConsentLog:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<PowDriverConsentLogVo> list(PowDriverConsentLogBo bo, PageQuery pageQuery) {
        return powDriverConsentLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机协议同意记录列表
     */
    @SaCheckPermission("settle:driverConsentLog:export")
    @Log(title = "司机协议同意记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverConsentLogBo bo, HttpServletResponse response) {
        List<PowDriverConsentLogVo> list = powDriverConsentLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机协议同意记录", PowDriverConsentLogVo.class, response);
    }

    /**
     * 获取司机协议同意记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("settle:driverConsentLog:query")
    @GetMapping("/{id}")
    @Enum2TextAspect
    public R<PowDriverConsentLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverConsentLogService.queryById(id));
    }

    /**
     * 新增司机协议同意记录
     */
    @SaCheckPermission("settle:driverConsentLog:add")
    @Log(title = "司机协议同意记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverConsentLogBo bo) {
        return toAjax(powDriverConsentLogService.insertByBo(bo));
    }

    /**
     * 修改司机协议同意记录
     */
    @SaCheckPermission("settle:driverConsentLog:edit")
    @Log(title = "司机协议同意记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverConsentLogBo bo) {
        return toAjax(powDriverConsentLogService.updateByBo(bo));
    }

    /**
     * 删除司机协议同意记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("settle:driverConsentLog:remove")
    @Log(title = "司机协议同意记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powDriverConsentLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
