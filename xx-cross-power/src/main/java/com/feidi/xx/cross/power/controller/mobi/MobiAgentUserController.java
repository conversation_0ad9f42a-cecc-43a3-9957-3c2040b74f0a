package com.feidi.xx.cross.power.controller.mobi;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.encrypt.annotation.ApiEncrypt;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.pojo.bo.PasswordForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 移动端 - 代理商账号管理
 * 前端访问路由地址为:/power/mobi/agent/user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MOBI_ROUTE_PREFIX+ "/agent/user")
public class MobiAgentUserController extends BaseController {

    private final IPowAgentUserService powAgentUserService;

    /**
     * 获取代理商用户详细信息
     *
     */
    @Log(title = "代理商用户", businessType = BusinessType.EXPORT)
    @GetMapping()
    public R<PowAgentUserVo> getInfo() {
        Long userId = LoginHelper.getUserId();
        return R.ok(powAgentUserService.queryById(userId));
    }

    /**
     * 修改密码
     *
     * @param bo 新旧密码
     */
    @PutMapping("/updatePwd")
    public R<Boolean> updatePwd(@Validated @RequestBody PasswordForm bo) {
        return R.ok(powAgentUserService.resetPwd(bo));
    }

}
