package com.feidi.xx.cross.power.domain.vo;

import java.math.BigDecimal;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机佣金比例视图对象 pow_driver_rate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverRate.class)
public class PowDriverRateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "平台编码")
    private String platformCode;

    /**
     * 司机ID
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 佣金比例
     */
    @ExcelProperty(value = "佣金比例")
    private BigDecimal rate;


}
