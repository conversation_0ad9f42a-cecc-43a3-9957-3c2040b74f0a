package com.feidi.xx.cross.power.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.operate.api.RemoteCityService;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;
import com.feidi.xx.cross.power.domain.PowAgentCity;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBatchBo;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentCityVo;
import com.feidi.xx.cross.power.mapper.PowAgentCityMapper;
import com.feidi.xx.cross.power.service.IPowAgentCityService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代理商城市Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAgentCityServiceImpl implements IPowAgentCityService {

    private final PowAgentCityMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteCityService remoteCityService;


    /**
     * 查询代理商城市
     *
     * @param id 主键
     * @return 代理商城市
     */
    @Override
    public PowAgentCityVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询代理商城市列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商城市分页列表
     */
    @Override
    public TableDataInfo<PowAgentCityVo> queryPageList(PowAgentCityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAgentCity> lqw = buildQueryWrapper(bo);
        Page<PowAgentCityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代理商城市列表
     *
     * @param bo 查询条件
     * @return 代理商城市列表
     */
    @Override
    public List<PowAgentCityVo> queryList(PowAgentCityBo bo) {
        LambdaQueryWrapper<PowAgentCity> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowAgentCity> buildQueryWrapper(PowAgentCityBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowAgentCity> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, PowAgentCity::getAgentId, bo.getAgentId());
        lqw.eq(bo.getCityId() != null, PowAgentCity::getCityId, bo.getCityId());
        return lqw;
    }

    /**
     * 新增代理商城市
     *
     * @param bo 代理商城市
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowAgentCityBo bo) {
        PowAgentCity add = MapstructUtils.convert(bo, PowAgentCity.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改代理商城市
     *
     * @param bo 代理商城市
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowAgentCityBo bo) {
        PowAgentCity update = MapstructUtils.convert(bo, PowAgentCity.class);
        validEntityBeforeSave(update);
        boolean ret = baseMapper.updateById(update) > 0;
        // 异步添加缓存
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);

        return ret;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAgentCity entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代理商城市信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBoToList(PowAgentCityBatchBo batchBo) {

        Map<Long, RemoteCityVo> cityId2Map = new HashMap<>();
        if (CollUtils.isNotEmpty(batchBo.getAgentCityBos())) {
            cityId2Map = remoteCityService.queryByIds(batchBo.getAgentCityBos().stream().map(PowAgentCityBo::getCityId).toList())
                    .stream().collect(Collectors.toMap(RemoteCityVo::getId, Function.identity()));
        }
        Map<Long, RemoteCityVo> finalCityId2Map = cityId2Map;
        List<PowAgentCity> agentCities = batchBo.getAgentCityBos().stream()
                .map(bo -> {
                    PowAgentCity add = MapstructUtils.convert(bo, PowAgentCity.class);
                    validEntityBeforeSave(add);
                    if (finalCityId2Map.containsKey(bo.getCityId())) {
                        add.setCityCode(finalCityId2Map.get(bo.getCityId()).getCityCode());
                    }
                    return add;
                }).collect(Collectors.toList());
        //根据城市id 删除
        LambdaQueryWrapper<PowAgentCity> qw =  new LambdaQueryWrapper<>();
        qw.eq(ObjectUtils.isNotNull(batchBo.getAgentId()), PowAgentCity::getAgentId, batchBo.getAgentId())
                .eq(ObjectUtils.isNotNull(batchBo.getCityId()), PowAgentCity::getCityId, batchBo.getCityId());
        baseMapper.delete(qw);
        return baseMapper.insertBatch(agentCities);
    }
    /**
     * 添加缓存
     *
     * @param agentCity 产品信息
     */
    private void addCache(PowAgentCity agentCity) {
        if (agentCity == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.create(agentCity.getAgentId());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(agentCity, RemoteAgentCityVo.class),
                PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.getDuration());
    }

    /**
     * 删除缓存
     *
     * @param agentId
     */
    private void deleteCache(Long agentId) {
        RedisUtils.deleteObject(PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.create(agentId));
    }
}
