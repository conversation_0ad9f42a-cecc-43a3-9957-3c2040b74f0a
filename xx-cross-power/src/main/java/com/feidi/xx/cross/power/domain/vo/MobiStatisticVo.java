package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.power.domain.PowAgent;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor()
@EqualsAndHashCode(callSuper = true)
public class MobiStatisticVo extends BaseEntity {

    /**
     * 总申请数
     */
    private Long submitSum;

    /**
     * 总通过数
     */
    private Long auditSuccessSum;

    /**
     * 待审核
     */
    private Long auditing;

    /**
     * 已通过
     */
    private Long auditSuccess;

    /**
     * 已驳回
     */
    private Long auditReject;
}
