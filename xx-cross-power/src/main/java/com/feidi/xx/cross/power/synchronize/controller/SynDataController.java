package com.feidi.xx.cross.power.synchronize.controller;

import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.synchronize.service.IPowSyncService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/data")
public class SynDataController extends BaseController {
    private final IPowSyncService powSyncService;

    /**
     * 同步司机数据
     *
     */
    @GetMapping("/driver")
    public Boolean driverSync() {
        return powSyncService.driverSync();
    }

    /**
     * 同步车辆信息
     */
    @GetMapping("/car")
    public Boolean carSync() {
        return powSyncService.carSync();
    }

    /**
     * 同步司机账户信息
     */
    @GetMapping("/driverAccount")
    public Boolean driverAccountSync() {
        return powSyncService.driverAccountSync();
    }

    /**
     * 同步司机证件信息
     */
    @GetMapping("/driverCerts")
    public Boolean driverCertsSync() {
        return powSyncService.driverCertsSync();
    }

    /**
     * 同步代理商信息
     */
    @GetMapping("/agent")
    public Boolean agentSync() {
        return powSyncService.agentSync();
    }
}
