package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteDriverLineService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverLineVo;
import com.feidi.xx.cross.power.domain.PowDriverLine;
import com.feidi.xx.cross.power.mapper.PowDriverLineMapper;
import com.feidi.xx.cross.power.service.IPowDriverLineService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 司机线路服务接口实现类
 * 司机线路服务
 *
 * <AUTHOR>
 * @date 2024/9/7
 * <AUTHOR>
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteDriverLineServiceImpl implements RemoteDriverLineService {

    private final PowDriverLineMapper baseMapper;
    /**
     * 司机线路服务
     */
    private final IPowDriverLineService driverLineService;

    /**
     *  获取司机线路
     * @param driverId 司机ID
     * @return
     */
    /**
     *  获取司机线路
     * @param driverId 司机ID
     * @return
     */
    @Override
    public List<Long> getDriverLine(Long driverId) {
        return driverLineService.listByDriverId(driverId);
    }

    @Override
    public Boolean driverHasLine(Long driverId, Long lineId) {
        return driverLineService.driverHasLine(driverId, lineId);
    }

    /**
     * 根据司机id获取司机线路信息
     *
     * @param driverId 司机id
     * @return 司机线路信息集合
     */
    @Override
    public List<RemoteDriverLineVo> getByDriverId(Long driverId) {
        LambdaQueryWrapper<PowDriverLine> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowDriverLine::getDriverId, driverId);
        List<PowDriverLine> powDriverLines = baseMapper.selectList(lqw);

        return BeanUtils.copyToList(powDriverLines, RemoteDriverLineVo.class);
    }

    @Override
    public RemoteDriverLineVo getByDriverIdAndLineId(Long driverId, Long lineId) {
        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PowDriverLine::getDriverId, driverId)
                .eq(PowDriverLine::getLineId, lineId)
                .orderByDesc(PowDriverLine::getCreateTime)
                .last(Constants.LIMIT_ONE);
        PowDriverLine powDriverLine = baseMapper.selectOne(lqw);
        return BeanUtils.copyProperties(powDriverLine, RemoteDriverLineVo.class);
    }
}
