package com.feidi.xx.cross.power.domain.vo;

import java.util.Date;

import com.feidi.xx.cross.power.domain.PowDriverCerts;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机证件视图对象 pow_driver_certs
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverCerts.class)
public class PowDriverCertsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 上级
     */
    @ExcelProperty(value = "上级")
    private Long driverId;

    /**
     * 证件所有人
     */
    @ExcelProperty(value = "证件所有人")
    private String certsOwner;

    /**
     * 证件类型
     */
    @ExcelProperty(value = "证件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "DriverCertTypeEnum")
    private String type;

    /**
     * ossId
     */
    @ExcelProperty(value = "ossId")
    private Long ossId;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;


}
