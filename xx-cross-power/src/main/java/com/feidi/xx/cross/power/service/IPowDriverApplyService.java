package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverAgreementForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverCarForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverDrivingForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverIdCardForm;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;


/**
 * 司机端 - 司机申请Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface IPowDriverApplyService {

    /**
     * 司机申请-上传身份证
     */
    Boolean uploadIdCard(PowDriverIdCardForm bo);

    /**
     * 司机申请-上传驾驶证
     */
    Boolean uploadDriving(PowDriverDrivingForm bo);

    /**
     * 司机申请-上传车辆信息
     */
    Boolean uploadCar(PowDriverCarForm bo);

    /**
     * 司机申请
     *
     * @param bo 司机申请bo
     * @return 是否申请成功
     */
    Boolean apply(PowDriverBo bo);

    /**
     * 司机申请 - 暂存数据
     * @param bo
     * @return
     */
    Boolean storage(PowDriverBo bo);

    /**
     * 司机申请 - 获取暂存数据
     * @param driverId
     * @return
     */
    PowDriverVo getStorage(Long driverId);

    /**
     * 获取审核结果
     * @return
     */
    PowDriverVo getResult();

    /**
     * 司机认证
     * @param ossId 图片id
     * @return 认证结果
     */
    Boolean uploadAuthImg(Long ossId);

    Boolean uploadAgreement(PowDriverAgreementForm bo);
}
