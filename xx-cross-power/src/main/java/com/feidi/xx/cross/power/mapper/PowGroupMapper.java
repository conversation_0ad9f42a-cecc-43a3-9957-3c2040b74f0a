package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.enums.power.DriverGroupTypeEnum;
import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 司机组Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface PowGroupMapper extends BaseMapperPlus<PowGroup, PowGroupVo> {
    /**
     * 获取默认司机组
     */
    default PowGroup getDefaultGroup() {
        return selectOne(Wrappers.<PowGroup>lambdaUpdate()
                .eq(PowGroup::getType, DriverGroupTypeEnum.PARTNER.getCode())
        );
    }
}
