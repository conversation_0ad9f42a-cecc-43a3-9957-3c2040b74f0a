package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.resource.api.RemoteALiOcrService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowCarBo;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.mapper.PowCarMapper;
import com.feidi.xx.cross.power.service.IPowCarService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowCarServiceImpl implements IPowCarService {

    private final PowCarMapper baseMapper;

    private final PowDriverMapper driverMapper;

    private final PowAgentMapper agentMapper;

    // 阿里云OCR 服务
    @DubboReference
    private final RemoteALiOcrService remoteALiOcrService;

    /**
     * 查询车辆
     *
     * @param id 主键
     * @return 车辆
     */
    @Override
    public PowCarVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 获取司机车辆信息
     * @param driverId 司机主键
     * @return
     */
    @Override
    public PowCarVo queryByDriver(Long driverId) {
        return baseMapper.selectVoOne(Wrappers.<PowCar>lambdaQuery().eq(PowCar::getDriverId, driverId), false);
    }

    /**
     * 批量获取车辆信息
     * @param ids
     * @return
     */
    @Override
    public List<PowCarVo> queryByDriverIds(List<Long> ids) {
        return baseMapper.selectVoList(Wrappers.<PowCar>lambdaQuery().in(PowCar::getDriverId, ids));
    }


    /**
     * 分页查询车辆列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 车辆分页列表
     */
    @Override
    public TableDataInfo<PowCarVo> queryPageList(PowCarBo bo, PageQuery pageQuery) {
        if (StrUtil.isNotBlank(bo.getDriverName())) {
            List<PowDriver> byName = driverMapper.getByName(bo.getDriverName());
            if (CollUtil.isNotEmpty(byName)) {
                List<Long> driverIds = StreamUtils.toList(byName, PowDriver::getId);
                bo.setDriverIds(driverIds);
            }
        }
        LambdaQueryWrapper<PowCar> lqw = buildQueryWrapper(bo);
        Page<PowCarVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        // 获取所有司机id并查询后设置司机名称
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<Long> driverIds = result.getRecords().stream().map(PowCarVo::getDriverId).toList();
            Map<Long, PowDriver> driverMap = getDriverMap(driverIds);
            Map<Long, String> agentMap = getAgentMap();
            for (PowCarVo vo : result.getRecords()) {
                PowDriver driver = driverMap.get(vo.getDriverId());
                if (driver != null) {
                    vo.setDriverName(driver.getName());
                    vo.setDriverPhone(driver.getPhone());
                    // 代理商名称
                    if (ArithUtils.isNotNull(driver.getAgentId())) {
                        String agentName = agentMap.get(driver.getAgentId());
                        vo.setAgentName(agentName);
                    }
                }
            }
        }
        return TableDataInfo.build(result);
    }

    private Map<Long, PowDriver> getDriverMap(List<Long> driverIds) {
        if (CollUtil.isNotEmpty(driverIds)) {
            return driverMapper.selectBatchIds(driverIds).stream().collect(Collectors.toMap(PowDriver::getId, Function.identity()));
        }
        return new HashMap<>();
    }

    private Map<Long, String> getAgentMap() {
        List<PowAgent> powAgents = agentMapper.selectList();
        Map<Long, String> agentMap = powAgents.stream().collect(Collectors.toMap(PowAgent::getId, PowAgent::getCompanyName));
        return agentMap;
    }

    /**
     * 查询符合条件的车辆列表
     *
     * @param bo 查询条件
     * @return 车辆列表
     */
    @Override
    public List<PowCarVo> queryList(PowCarBo bo) {
        LambdaQueryWrapper<PowCar> lqw = buildQueryWrapper(bo);
        List<PowCarVo> vos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(vos)) {
            List<Long> driverIds = vos.stream().map(PowCarVo::getDriverId).toList();
            Map<Long, PowDriver> driverMap = getDriverMap(driverIds);
            for (PowCarVo vo : vos) {
                PowDriver driver = driverMap.get(vo.getDriverId());
                if (driver != null) {
                    vo.setDriverName(driver.getName());
                    vo.setDriverPhone(driver.getPhone());
                }
            }
        }
        return vos;
    }

    private LambdaQueryWrapper<PowCar> buildQueryWrapper(PowCarBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowCar> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(bo.getDriverIds()), PowCar::getDriverId, bo.getDriverIds());
        lqw.eq(bo.getDriverId() != null, PowCar::getDriverId, bo.getDriverId());
        lqw.eq(StringUtils.isNotBlank(bo.getCarNumber()), PowCar::getCarNumber, bo.getCarNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getCarBrand()), PowCar::getCarBrand, bo.getCarBrand());
        lqw.eq(StringUtils.isNotBlank(bo.getCarStyle()), PowCar::getCarStyle, bo.getCarStyle());
        lqw.eq(StringUtils.isNotBlank(bo.getDriveType()), PowCar::getDriveType, bo.getDriveType());
        lqw.eq(StringUtils.isNotBlank(bo.getVin()), PowCar::getVin, bo.getVin());
        lqw.eq(StringUtils.isNotBlank(bo.getEngine()), PowCar::getEngine, bo.getEngine());
        return lqw;
    }

    /**
     * 新增车辆
     *
     * @param bo 车辆
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowCarBo bo) {
        PowCar add = MapstructUtils.convert(bo, PowCar.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改车辆
     *
     * @param bo 车辆
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowCarBo bo) {
        PowCar update = MapstructUtils.convert(bo, PowCar.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowCar entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除车辆信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
