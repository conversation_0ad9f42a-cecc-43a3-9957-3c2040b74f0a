package com.feidi.xx.cross.power.controller.agent;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.bo.PowDriverAccountBo;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.service.IPowDriverAccountService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 司机提现账户管理
 * 前端访问路由地址为:/power/driver/account
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/driver/account")
public class AgtDriverAccountController extends BaseController {

    private final IPowDriverAccountService driverAccountService;

    /**
     * 列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<PowDriverAccountVo> list(PowDriverAccountBo bo, PageQuery pageQuery) {
        return driverAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出
     *
     * @param bo
     */
    @PostMapping("/export")
    public void export(@RequestBody PowDriverAccountBo bo,HttpServletResponse response) {
        List<PowDriverAccountVo> list = driverAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机账户", PowDriverAccountVo.class, response);
    }

    /**
     * 详情
     *
     * @return
     */
    @GetMapping("/{id}")
    public R<PowDriverAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(driverAccountService.queryById(id));
    }

    /**
     * 编辑
     * <p>
     *  TODO 如果司机是禁用状态、禁止修改未启用
     *
     * @return
     */
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverAccountBo bo) {
        return toAjax(driverAccountService.updateByBo(bo));
    }

}
