package com.feidi.xx.cross.power.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.domain.PowDriverChangeLog;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.bo.PowDriverChangeLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverChangeLogVo;
import com.feidi.xx.cross.power.formatter.ChangeLogContext;
import com.feidi.xx.cross.power.formatter.SimpleChangeLogFormatter;
import com.feidi.xx.cross.power.mapper.PowDriverChangeLogMapper;
import com.feidi.xx.cross.power.service.IPowDriverChangeLogService;
import com.feidi.xx.system.api.RemoteUserService;
import com.feidi.xx.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机信息变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RequiredArgsConstructor
@Service
public class PowDriverChangeLogServiceImpl implements IPowDriverChangeLogService {

    private final PowDriverChangeLogMapper baseMapper;

    private final ObjectMapper objectMapper;

    private final SimpleChangeLogFormatter changeLogFormatter;

    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;

    /**
     * 查询司机信息变更记录
     *
     * @param id 主键
     * @return 司机信息变更记录
     */
    @Override
    public PowDriverChangeLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机信息变更记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机信息变更记录分页列表
     */
    @Override
    public TableDataInfo<PowDriverChangeLogVo> queryPageList(PowDriverChangeLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverChangeLog> lqw = buildQueryWrapper(bo);
        Page<PowDriverChangeLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        setAssociationInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机信息变更记录列表
     *
     * @param bo 查询条件
     * @return 司机信息变更记录列表
     */
    @Override
    public List<PowDriverChangeLogVo> queryList(PowDriverChangeLogBo bo) {
        LambdaQueryWrapper<PowDriverChangeLog> lqw = buildQueryWrapper(bo);
        List<PowDriverChangeLogVo> voList = baseMapper.selectVoList(lqw);
        setAssociationInfo(voList);
        return voList;
    }

    /**
     * 设置关联信息
     */
    private void setAssociationInfo(List<PowDriverChangeLogVo> records) {
        if (CollUtils.isEmpty(records)) {
            return;
        }

        // 收集用户ID和代理商ID
        Set<Long> userIds = records.stream()
                .map(PowDriverChangeLogVo::getCreateBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Long> agentIds = records.stream()
                .map(PowDriverChangeLogVo::getAgentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (userIds.isEmpty() && agentIds.isEmpty()) {
            return;
        }

        // 并行查询用户和代理商信息
        Map<Long, RemoteUserVo> userMap = !userIds.isEmpty() ?
                remoteUserService.selectListByIds(new ArrayList<>(userIds))
                        .stream()
                        .collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity(), (k1, k2) -> k1)) :
                Collections.emptyMap();

        Map<Long, RemoteAgentVo> agentMap = !agentIds.isEmpty() ?
                remoteAgentService.getAgentInfoById(new ArrayList<>(agentIds))
                        .stream()
                        .collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity(), (k1, k2) -> k1)) :
                Collections.emptyMap();

        // 批量设置关联信息
        records.forEach(vo -> {
            userMap.computeIfPresent(vo.getCreateBy(), (k, user) -> {
                vo.setCreateName(user.getUserName());
                return user;
            });
            agentMap.computeIfPresent(vo.getAgentId(), (k, agent) -> {
                vo.setAgentName(agent.getCompanyName());
                return agent;
            });
        });
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PowDriverChangeLog> buildQueryWrapper(PowDriverChangeLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverChangeLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowDriverChangeLog::getDriverId, bo.getDriverId());
        lqw.eq(bo.getGroupId() != null, PowDriverChangeLog::getGroupId, bo.getGroupId());
        lqw.eq(bo.getAgentId() != null, PowDriverChangeLog::getAgentId, bo.getAgentId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), PowDriverChangeLog::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), PowDriverChangeLog::getPhone, bo.getPhone());
        lqw.like(StringUtils.isNotBlank(bo.getFieldName()), PowDriverChangeLog::getFieldName, bo.getFieldName());
        lqw.eq(StringUtils.isNotBlank(bo.getOldValue()), PowDriverChangeLog::getOldValue, bo.getOldValue());
        lqw.eq(StringUtils.isNotBlank(bo.getNewValue()), PowDriverChangeLog::getNewValue, bo.getNewValue());
        lqw.eq(StringUtils.isNotBlank(bo.getExtra()), PowDriverChangeLog::getExtra, bo.getExtra());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), PowDriverChangeLog::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getMsg()), PowDriverChangeLog::getMsg, bo.getMsg());
        if (bo.getCreateTimeRange() != null) {
            boolean b = bo.getCreateTimeRange().getStartTime() != null && bo.getCreateTimeRange().getEndTime() != null;
            lqw.between(b, PowDriverChangeLog::getCreateTime, bo.getCreateTimeRange().getStartTime(), bo.getCreateTimeRange().getEndTime());
        }
        lqw.orderByDesc(PowDriverChangeLog::getCreateTime);
        lqw.orderByDesc(PowDriverChangeLog::getId);
        return lqw;
    }


    @SneakyThrows
    @Override
    public void create(PowDriverBo old, PowDriverBo newBo, String title) {
        if (old == null || newBo == null) {
            return;
        }
        //TODO R diff bo field 使用工具对比差异
        PowDriverChangeLog powDriverChangeLog = new PowDriverChangeLog();
        powDriverChangeLog.setDriverId(newBo.getId());
        powDriverChangeLog.setGroupId(newBo.getGroupId());
        powDriverChangeLog.setAgentId(newBo.getAgentId());
        powDriverChangeLog.setName(newBo.getName());
        powDriverChangeLog.setPhone(newBo.getPhone());

        powDriverChangeLog.setTitle(title);
        if (!Objects.equals(old.getGroupId(), newBo.getGroupId())) {
            powDriverChangeLog.setFieldName("groupId");
            powDriverChangeLog.setOldValue(old.getGroupId() + "");
            powDriverChangeLog.setNewValue(newBo.getGroupId() + "");

            // #代表使用spel处理，{name} 这种单纯文本替换 {}用于分割表达式。
            String template = "将 {main} 的{fieldDisplayName}从 {#driverGroup.format(#oldValue)} 变更为 {#driverGroup.format(#newValue)}";
            ChangeLogContext context = ChangeLogContext.builder()
                    .main(newBo.getName())
                    .fieldName("司机组")
                    .fieldDisplayName("司机组")
                    .oldValue(old.getGroupId())
                    .newValue(newBo.getGroupId())
                    .build();
            String msg = changeLogFormatter.format(template, context);
            powDriverChangeLog.setMsg(msg);
            baseMapper.insert(powDriverChangeLog);
        }
    }
}
