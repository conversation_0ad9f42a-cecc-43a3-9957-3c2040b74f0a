package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverConsentSceneEnum;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.cross.power.domain.PowDriverAccount;
import com.feidi.xx.cross.power.domain.PowDriverConsentLog;
import com.feidi.xx.cross.power.domain.bo.PowDriverAccountBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.mapper.PowDriverAccountMapper;
import com.feidi.xx.cross.power.mapper.PowDriverConsentLogMapper;
import com.feidi.xx.cross.power.service.IPowDriverAccountService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 司机账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowDriverAccountServiceImpl implements IPowDriverAccountService {

    private final PowDriverAccountMapper baseMapper;
    private final PowDriverConsentLogMapper consentLogMapper;


    @DubboReference
    private final RemoteDriverService remoteDriverService;

    /**
     * 查询司机账户
     *
     * @param id 主键
     * @return 司机账户
     */
    @Override
    public PowDriverAccountVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机账户分页列表
     */
    @Override
    public TableDataInfo<PowDriverAccountVo> queryPageList(PowDriverAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverAccount> lqw = buildQueryWrapper(bo);
        Page<PowDriverAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机账户列表
     *
     * @param bo 查询条件
     * @return 司机账户列表
     */
    @Override
    public List<PowDriverAccountVo> queryList(PowDriverAccountBo bo) {
        LambdaQueryWrapper<PowDriverAccount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowDriverAccount> buildQueryWrapper(PowDriverAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowDriverAccount::getDriverId, bo.getDriverId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), PowDriverAccount::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), PowDriverAccount::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getAccount()), PowDriverAccount::getAccount, bo.getAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), PowDriverAccount::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDefaulted()), PowDriverAccount::getDefaulted, bo.getDefaulted());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PowDriverAccount::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增司机账户
     *
     * @param bo 司机账户
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PowDriverAccountBo bo) {
        //Assert.isTrue(SmsUtil.verify(LoginHelper.getUserPhone(), SmsCodeTypeEnum.BIND_ACCOUNT, bo.getSmsCode()), SmsConstant.SMS_CAPTCHA_WRONG);
        PowDriverAccount add = MapstructUtils.convert(bo, PowDriverAccount.class);
        add.setPhone(LoginHelper.getUserPhone());
        add.setStatus(StatusEnum.ENABLE.getCode());
        // FIXME: 2024/10/14 现在只开放支付宝 前端控制
//        add.setType(AccountTypeEnum.ALIPAY.getCode());
        validEntityBeforeSave(add);
        boolean ret = baseMapper.insert(add) > 0;
        //if (ret) {
        //    // 删除验证码
        //    SmsUtil.remove(LoginHelper.getUserPhone(), SmsCodeTypeEnum.BIND_ACCOUNT);
        //}
        return ret;
    }

    /**
     * 修改司机账户
     *
     * @param bo 司机账户
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PowDriverAccountBo bo) {
        //Assert.isTrue(SmsUtil.verify(LoginHelper.getUserPhone(), SmsCodeTypeEnum.BIND_ACCOUNT, bo.getSmsCode()), SmsConstant.SMS_CAPTCHA_WRONG);
        PowDriverAccount update = MapstructUtils.convert(bo, PowDriverAccount.class);
        update.setPhone(LoginHelper.getUserPhone());
        validEntityBeforeSave(update);
        boolean ret = baseMapper.updateById(update) > 0;
        //if (ret) {
        //    // 删除验证码
        //    SmsUtil.remove(LoginHelper.getUserPhone(), SmsCodeTypeEnum.BIND_ACCOUNT);
        //}
        return ret;
    }

    private void saveConsentLog(RemoteDriverVo driverInfo){
        PowDriverConsentLog log = new PowDriverConsentLog();
        log.setTenantId("000000");
        log.setScene(DriverConsentSceneEnum.BIND.getCode());
        log.setDriverId(driverInfo.getId());
        log.setAgentId(driverInfo.getAgentId());
        log.setAgentName(driverInfo.getCompanyName());
        log.setPhone(driverInfo.getPhone());
        log.setIp(ServletUtils.getClientIP());
        log.setConsentTime(new Date());
        log.setName("《钱包提现协议》");
        consentLogMapper.insert(log);
    }
    /**
     * 根据司机id查询司机账户信息
     *
     * @param driverId 司机id
     * @return 司机账户信息
     */
    @Override
    public PowDriverAccountVo queryByDriverId(Long driverId) {
        LambdaQueryWrapper<PowDriverAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowDriverAccount::getDriverId, driverId);
        return baseMapper.selectVoOne(lqw);    }

    /**
     * 根据司机ids查询司机账户信息
     *
     * @param driverIds 司机ids
     * @return 司机账户信息
     */
    @Override
    public List<PowDriverAccountVo> queryByDriverIds(List<Long> driverIds) {
        LambdaQueryWrapper<PowDriverAccount> lqw = Wrappers.lambdaQuery();
        lqw.in(PowDriverAccount::getDriverId, driverIds);
        return baseMapper.selectVoList(lqw);    }

    /**
     * 根据司机id修改司机账户信息
     *
     * @param account 账户
     * @param accountName 账户名称
     * @param driverId 司机id
     */
    @Override
    public boolean updateAccountAndNameByDriverId(String account, String accountName, Long driverId) {
        LambdaUpdateWrapper<PowDriverAccount> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(account), PowDriverAccount::getAccount, account)
                .set(StringUtils.isNotBlank(accountName), PowDriverAccount::getName, accountName)
                .set(PowDriverAccount::getUpdateTime, DateUtils.getNowDate())
                .eq(PowDriverAccount::getDriverId, driverId);

        return baseMapper.update(updateWrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriverAccount entity){
        if (AccountTypeEnum.ALIPAY.getCode().equals(entity.getType())) {
            boolean isValid = entity.getAccount().matches(RegexPool.EMAIL) || entity.getAccount().matches(RegexPool.MOBILE);
            Assert.isTrue(isValid, "请输入正确的账号，账号只能是邮箱或手机号");
        }

        RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(entity.getDriverId());
        Assert.notNull(driverInfo, "司机不存在");
        if (ObjectUtil.isNull(entity.getId())) {
            // 新增的时候判断是否已有
            PowDriverAccount driverAccount = baseMapper.selectByDriverIdAndType(entity.getDriverId(), entity.getType());
            Assert.isNull(driverAccount, "同类型账号已存在，请勿重复添加");
            // 支付宝的话手机号必须一样
            if (AccountTypeEnum.ALIPAY.getCode().equals(entity.getType())) {
                entity.setDefaulted(IsYesEnum.YES.getCode());
            } else {
                entity.setDefaulted(IsYesEnum.NO.getCode());
            }
        }
        saveConsentLog(driverInfo);

    }

    /**
     * 校验并批量删除司机账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}
