package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import com.feidi.xx.cross.power.domain.bo.PowGroupBo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.service.IPowGroupService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机组
 * 前端访问路由地址为:/power/group
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/group")
public class PowGroupController extends BaseController {

    private final IPowGroupService powGroupService;

    /**
     * 查询司机组列表
     */
    @SaCheckPermission("power:powerGroup:list")
    @GetMapping("/list")
    public TableDataInfo<PowGroupVo> list(PowGroupBo bo, PageQuery pageQuery) {
        return powGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询司机组列表-全部
     * @param bo
     * @return
     */
    @SaCheckPermission("power:powerGroup:list")
    @GetMapping("/list/all")
    public R<List<PowGroupVo>> list(PowGroupBo bo) {
        return R.ok(powGroupService.queryList(bo,PowGroupVo.class));
    }

    /**
     * 导出司机组列表
     */
    @SaCheckPermission("power:powerGroup:export")
    @Log(title = "司机组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowGroupBo bo, HttpServletResponse response) {
        List<PowGroupVo> list = powGroupService.queryList(bo,PowGroupVo.class);
        ExcelUtil.exportExcel(list, "司机组", PowGroupVo.class, response);
    }

    /**
     * 获取司机组详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerGroup:query")
    @GetMapping("/{id}")
    public R<PowGroupVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powGroupService.queryById(id));
    }

    /**
     * 新增司机组
     */
    @SaCheckPermission("power:powerGroup:add")
    @Log(title = "司机组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowGroupBo bo) {
        return toAjax(powGroupService.insertByBo(bo));
    }

    /**
     * 修改司机组
     */
    @SaCheckPermission("power:powerGroup:edit")
    @Log(title = "司机组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowGroupBo bo) {
        return toAjax(powGroupService.updateByBo(bo));
    }

    /**
     * 删除司机组
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerGroup:remove")
    @Log(title = "司机组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powGroupService.deleteWithValidByIds(List.of(ids), true));
    }
}
