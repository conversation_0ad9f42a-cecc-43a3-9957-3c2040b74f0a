package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowDriverLine;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverLineForm;
import com.feidi.xx.cross.power.mapper.PowAgentLineMapper;
import com.feidi.xx.cross.power.mapper.PowDriverLineMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.cross.power.service.IPowDriverLineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机线路Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PowDriverLineServiceImpl implements IPowDriverLineService {

    private final PowDriverLineMapper baseMapper;
    private final PowDriverMapper driverMapper;
    private final PowAgentLineMapper agentLineMapper;
    @DubboReference
    private final RemoteLineService remoteLineService;

    @Override
    public List<Long> listByDriverId(Long driverId) {
        List<PowDriverLine> relations = baseMapper.listByDriverId(driverId);
        return StreamUtils.toList(relations, PowDriverLine::getLineId);
    }

    @Override
    public List<Long> listByLineId(Long lineId) {
        List<PowDriverLine> relations = baseMapper.listByLineId(lineId);
        return StreamUtils.toList(relations, PowDriverLine::getDriverId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignLine(PowDriverLineForm bo) {
        PowDriver driver = driverMapper.selectById(bo.getDriverId());
        Assert.isTrue(ObjectUtil.isNotEmpty(driver), "司机不存在");
        if (ArithUtils.isNotNull(driver.getAgentId())) {
            Assert.isTrue(LoginHelper.getAgentId().equals(driver.getAgentId()), "非司机所属代理商，无法操作");
            List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(driver.getAgentId());
            List<Long> lineIds = agentLines.stream().map(PowAgentLine::getLineId).toList();
            Assert.isTrue(CollUtil.containsAll(lineIds, bo.getLineIds()), "只能设置司机所属代理商所分配到的线路");
        }

        // 清空司机下的线路
        baseMapper.deleteByDriverId(bo.getDriverId());
        // 新增线路
        if (CollUtil.isNotEmpty(bo.getLineIds())) {
            List<PowAgentLine> childLines = agentLineMapper.listByParentId(driver.getAgentId());
            Map<Long, Long> childLineMap = childLines.stream().collect(Collectors.toMap(PowAgentLine::getLineId, PowAgentLine::getAgentId));

            List<RemoteLineVo> lineVos = remoteLineService.queryByLineIds(bo.getLineIds());
            Map<Long, RemoteLineVo> lineMap = StreamUtils.toMap(lineVos, RemoteLineVo::getId, Function.identity());
            List<PowDriverLine> addLines = new ArrayList<>();
            for (Long lineId : bo.getLineIds()) {
                RemoteLineVo line = lineMap.getOrDefault(lineId, new RemoteLineVo());
                Assert.isTrue(childLineMap.get(lineId) == null, StrUtil.format("线路【{}】已分配给子代理商，无法继续分配", line.getName()));

                PowDriverLine driverLine = new PowDriverLine(driver.getAgentId(), bo.getDriverId(), lineId);
                addLines.add(driverLine);
            }
            // 删除司机线路
            this.deleteDriverLineCache(bo.getDriverId());
            baseMapper.insertBatch(addLines);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignDriver(PowDriverLineForm bo) {
        Long agentId = LoginHelper.getAgentId();
        Assert.isTrue(UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType()), "当前接口仅限代理商使用");

        List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(agentId);
        List<Long> lineIds = agentLines.stream().map(PowAgentLine::getLineId).toList();
        Assert.isTrue(CollUtil.contains(lineIds, bo.getLineId()), "只能设置司机所属代理商所分配到的线路");

        // 清空线路下的司机
        baseMapper.deleteByLineId(bo.getLineId());

        if (CollUtil.isNotEmpty(bo.getDriverIds())) {
            List<PowDriver> drivers = driverMapper.selectBatchIds(bo.getDriverIds());
            Map<Long, PowDriver> driverMap = StreamUtils.toMap(drivers, PowDriver::getId, Function.identity());

            List<PowAgentLine> childLines = agentLineMapper.listByParentId(agentId);
            Map<Long, Long> childLineMap = childLines.stream().collect(Collectors.toMap(PowAgentLine::getLineId, PowAgentLine::getAgentId));

            List<PowDriverLine> addLines = new ArrayList<>();
            for (Long driverId : bo.getDriverIds()) {
                PowDriver driver = driverMap.get(driverId);
                Assert.isTrue(agentId.equals(driver.getAgentId()), "非司机所属代理商，无法操作");

                Assert.isTrue(childLineMap.get(bo.getLineId()) == null, "当前线路已分配给子代理商，无法继续分配");

                PowDriverLine driverLine = new PowDriverLine(agentId, driverId, bo.getLineId());
                addLines.add(driverLine);

                // 删除司机线路
                this.deleteDriverLineCache(driverId);
            }
            baseMapper.insertBatch(addLines);
        }


        return true;
    }

    @Override
    public Boolean driverHasLine(Long driverId, Long lineId) {

        LambdaQueryWrapper<PowDriverLine> lqw = new LambdaQueryWrapper<>();

        lqw.eq(PowDriverLine::getDriverId, driverId)
               .eq(PowDriverLine::getLineId, lineId);

        return CollUtil.isNotEmpty(baseMapper.selectList(lqw));
    }

    /**
     * 删除司机线路缓存
     *
     * @param driverId 司机id
     */
    private void deleteDriverLineCache(Long driverId) {
        try {
            String cacheKey = PowCacheKeyEnum.POW_DRIVER_LINE_CACHE_KEY.create(driverId);
            RedisUtils.deleteObject(cacheKey);
        } catch (Exception e) {
            log.error("删除司机线路缓存失败");
        }
    }
}
