package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.power.domain.bo.PowDriverConsentLogBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverConsentLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 司机协议同意记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface IPowDriverConsentLogService {

    /**
     * 查询司机协议同意记录
     *
     * @param id 主键
     * @return 司机协议同意记录
     */
    PowDriverConsentLogVo queryById(Long id);

    /**
     * 分页查询司机协议同意记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机协议同意记录分页列表
     */
    TableDataInfo<PowDriverConsentLogVo> queryPageList(PowDriverConsentLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机协议同意记录列表
     *
     * @param bo 查询条件
     * @return 司机协议同意记录列表
     */
    List<PowDriverConsentLogVo> queryList(PowDriverConsentLogBo bo);

    /**
     * 新增司机协议同意记录
     *
     * @param bo 司机协议同意记录
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverConsentLogBo bo);

    /**
     * 修改司机协议同意记录
     *
     * @param bo 司机协议同意记录
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverConsentLogBo bo);

    /**
     * 校验并批量删除司机协议同意记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
