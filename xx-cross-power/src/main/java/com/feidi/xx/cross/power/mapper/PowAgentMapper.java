package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 代理商列表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowAgentMapper extends BaseMapperPlus<PowAgent, PowAgentVo> {

    default List<PowAgent> listByParentId(Long agentId){
        return selectList(new LambdaQueryWrapper<PowAgent>().eq(PowAgent::getParentId, agentId));
    }

    default List<PowAgent> listByParentIds(Collection<Long> agentIds){
        return selectList(new LambdaQueryWrapper<PowAgent>().in(PowAgent::getParentId, agentIds));
    }

    default boolean existsByCompany(String companyName, String taxNo){
        return exists(Wrappers.<PowAgent>lambdaQuery()
                .nested(e -> e.eq(PowAgent::getCompanyName, companyName)
                        .eq(PowAgent::getTaxNo, taxNo))
        );
    }

    default void disableAgent(Long... agentIds) {
        update(Wrappers.<PowAgent>lambdaUpdate()
                .in(PowAgent::getId, Arrays.asList(agentIds))
                .set(PowAgent::getStatus, StatusEnum.DISABLE.getCode())
                .eq(PowAgent::getStatus, StatusEnum.ENABLE.getCode())
        );
    }

    default void enableAgent(Long... agentIds) {
        update(Wrappers.<PowAgent>lambdaUpdate()
                .in(PowAgent::getId, Arrays.asList(agentIds))
                .set(PowAgent::getStatus, StatusEnum.ENABLE.getCode())
                .eq(PowAgent::getStatus, StatusEnum.DISABLE.getCode())
        );
    }

}
