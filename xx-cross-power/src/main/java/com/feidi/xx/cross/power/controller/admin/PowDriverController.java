package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.bo.*;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.service.IPowDriverApplyService;
import com.feidi.xx.cross.power.service.IPowDriverLineService;
import com.feidi.xx.cross.power.service.IPowDriverService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机
 * 前端访问路由地址为:/power/driver
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver")
public class PowDriverController extends BaseController {

    private final IPowDriverService powDriverService;
    private final IPowDriverLineService powDriverLineService;
    private final IPowDriverApplyService powDriverApplyService;

    /**
     * 查询司机列表
     */
    @SaCheckPermission("power:powerDriver:list")
    @GetMapping("/list")
    @Id2NameAspect
    public TableDataInfo<PowDriverVo> list(PowDriverBo bo, PageQuery pageQuery) {
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        bo.setNeIdentity(DriverIdentityEnum.NO.getCode());
        return powDriverService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询司机列表-全部
     */
    @SaCheckPermission("power:powerDriver:list")
    @GetMapping("/list/all")
    public List<PowDriverVo> list(PowDriverBo bo) {
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        bo.setNeIdentity(DriverIdentityEnum.NO.getCode());
        return powDriverService.queryList(bo);
    }

    /**
     * 导出司机列表
     */
    @SaCheckPermission("power:powerDriver:export")
    @Log(title = "司机", businessType = BusinessType.EXPORT)
    @Download(name="司机",module = ModuleConstants.POWER, mode="no")
    @PostMapping("/export")
    public Object export(PowDriverBo bo, HttpServletResponse response) {
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        bo.setNeIdentity(DriverIdentityEnum.NO.getCode());
        List<PowDriverVo> list = powDriverService.queryList(bo);
        //ExcelUtil.exportExcel(list, "司机", PowDriverVo.class, response);
        //List<FinBillExportVo> exportVos = MapstructUtils.convert(list, FinBillExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "订单对账", PowDriverVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取司机详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerDriver:query")
    @GetMapping("/{id}")
    @Id2NameAspect
    public R<PowDriverVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powDriverService.queryById(id));
    }

    /**
     * 新增司机
     */
    @SaCheckPermission("power:powerDriver:add")
    @Log(title = "司机", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverBo bo) {
        return toAjax(powDriverService.insertByBo(bo));
    }

    /**
     * 修改司机
     */
    @SaCheckPermission("power:powerDriver:edit")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverBo bo) {
        return toAjax(powDriverService.updateByBo(bo));
    }

    /**
     * 封禁司机
     *
     * @param id 主键串
     */
    @SaCheckPermission("power:powerDriver:seal")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @PutMapping("/seal/{id}")
    public R<Void> seal(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(powDriverService.sealById(id, true));
    }

    /**
     * 解封司机
     *
     * @param id 主键串
     */
    @SaCheckPermission("power:powerDriver:unseal")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @PutMapping("/unseal/{id}")
    public R<Void> unseal(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(powDriverService.unsealById(id));
    }

    /**
     * 分配线路
     * @return
     */
    @SaCheckPermission("power:powerDriver:assignLine")
    @Log(title = "司机", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/assignLine")
    public R<Boolean> assignLine(@Validated @RequestBody PowDriverLineForm bo) {
        return R.ok(powDriverLineService.assignLine(bo));
    }

    /**
     * 调度获取司机
     */
    @SaCheckPermission("power:powerDriver:dispatch")
    @Log(title = "司机", businessType = BusinessType.DISPATCH)
    @GetMapping("/dispatch")
    public TableDataInfo<PowDriverVo> dispatch(@Validated PowDriverBo bo, PageQuery pageQuery) {
        /// 是否接单
        bo.setIsReceive(IsYesEnum.YES.getCode());
        bo.setQueryPendingTripOrders(true);
        return powDriverService.dispatch(bo, pageQuery);
    }

    /**
     * 导入司机
     */
    @SaCheckPermission("power:powerDriver:import")
    @Log(title = "司机", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importDriver", consumes = "multipart/form-data")
    public R<Void> importDriver(@RequestPart("file") MultipartFile file) {
        powDriverService.importDriver(file);
        return R.ok();
    }

    /**
     * 修改司机组
     */
    @RepeatSubmit()
    @SaCheckPermission("power:powerDriver:group")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @PostMapping("/editGroup")
    public R<Boolean> editGroup(@RequestBody PowDriverBo bo) {
        return R.ok(powDriverService.updateDriverGroup(bo));
    }

    /**
     * 司机身份信息修改
     */
    @SaCheckPermission("power:powerDriver:identify")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/identify")
    public R<Boolean> idCard(@RequestBody @Validated PowDriverIdCardForm bo) {
        return R.ok(powDriverApplyService.uploadIdCard(bo));
    }

    /**
     * 司机驾驶证信息修改
     */
    @SaCheckPermission("power:powerDriver:driverLicense")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/driverLicense")
    public R<Boolean> driving(@RequestBody @Validated PowDriverDrivingForm bo) {
        return R.ok(powDriverApplyService.uploadDriving(bo));
    }

    /**
     * 司机车辆信息修改
     */
    @SaCheckPermission("power:powerDriver:car")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/car")
    public R<Boolean> car(@RequestBody @Validated PowDriverCarForm bo) {
        return R.ok(powDriverApplyService.uploadCar(bo));
    }

    /**
     * 司机代扣协议信息
     */
    @SaCheckPermission("power:powerDriver:identify")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/agreement")
    public R<Boolean> agreement(@RequestBody @Validated PowDriverAgreementForm bo) {
        return R.ok(powDriverApplyService.uploadAgreement(bo));
    }

    /**
     * 司机位置
     * @param bo
     * @return
     */
    @SaCheckPermission("power:driver:driverLocation")
    @RepeatSubmit()
    @PostMapping("/position/all")
    public R<List<PowDriverVo>> positionAll(@RequestBody PowDriverPositionForm bo) {
        return R.ok(powDriverService.positionAll(bo));
    }
}
