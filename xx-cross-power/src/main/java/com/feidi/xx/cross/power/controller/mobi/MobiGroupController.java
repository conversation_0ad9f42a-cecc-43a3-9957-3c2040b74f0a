package com.feidi.xx.cross.power.controller.mobi;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowGroupBo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.service.IPowGroupService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 司机组
 * 前端访问路由地址为:/power/mobi/group
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MOBI_ROUTE_PREFIX + "/group")
public class MobiGroupController extends BaseController {

    private final IPowGroupService powGroupService;

    /**
     * 查询司机组列表
     */
    @SaCheckPermission("power:powerGroup:list")
    @GetMapping("/list")
    public TableDataInfo<PowGroupVo> list(PowGroupBo bo, PageQuery pageQuery) {
        return powGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询司机组列表-全部
     * @param bo
     * @return
     */
    @SaCheckPermission("power:powerGroup:list")
    @GetMapping("/list/all")
    public R<List<PowGroupVo>> list(PowGroupBo bo) {
        return R.ok(powGroupService.queryList(bo,PowGroupVo.class));
    }
}
