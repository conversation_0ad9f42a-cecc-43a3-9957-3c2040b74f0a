package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.cross.power.domain.vo.PowAgentLineVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;

/**
 * 代理商线路Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowAgentLineMapper extends BaseMapperPlus<PowAgentLine, PowAgentLineVo> {

    default List<PowAgentLine> listByAgentId(Long agentId) {
        LambdaQueryWrapper<PowAgentLine> lqw = new LambdaQueryWrapper<PowAgentLine>()
                .eq(PowAgentLine::getAgentId, agentId);
        return selectList(lqw);
    }

    /**
     * 获取代理商名下的子代理商的线路关系
     * @param agentId
     * @return
     */
    default List<PowAgentLine> listByParentId(Long agentId) {
        LambdaQueryWrapper<PowAgentLine> lqw = new LambdaQueryWrapper<PowAgentLine>()
                .eq(PowAgentLine::getParentId, agentId);
        return selectList(lqw);
    }

    default List<PowAgentLine> listByLineId(Long lineId) {
        LambdaQueryWrapper<PowAgentLine> lqw = new LambdaQueryWrapper<PowAgentLine>()
                .eq(PowAgentLine::getLineId, lineId);
        return selectList(lqw);
    }

    default List<PowAgentLine> listByLineIds(Collection<Long> lineIds) {
        LambdaQueryWrapper<PowAgentLine> lqw = new LambdaQueryWrapper<PowAgentLine>()
                .in(PowAgentLine::getLineId, lineIds);
        return selectList(lqw);
    }

    default int deleteByAgentId(Long agentId) {
        return delete(Wrappers.<PowAgentLine>lambdaQuery()
                .eq(PowAgentLine::getAgentId, agentId)
        );
    }

    default int deleteByLineId(Long lineId) {
        return delete(Wrappers.<PowAgentLine>lambdaQuery()
                .eq(PowAgentLine::getLineId, lineId));
    }

    default boolean deleteByLineIds(Long agentId, Collection<Long> lineIds) {
        return delete(new LambdaQueryWrapper<PowAgentLine>()
                .eq(PowAgentLine::getAgentId, agentId)
                .in(PowAgentLine::getLineId, lineIds)) > 0;
    }


    default int deleteByAgentIds(Long lineId, Collection<Long> agentIds) {
        return delete(Wrappers.<PowAgentLine>lambdaQuery()
                .eq(PowAgentLine::getLineId, lineId)
                .in(PowAgentLine::getAgentId, agentIds));
    }
}
