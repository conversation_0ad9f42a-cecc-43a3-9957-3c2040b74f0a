package com.feidi.xx.cross.power.domain.bo;


import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 车辆业务对象 pow_car
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowCar.class, reverseConvertGenerate = false)
public class PowCarBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 司机
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机ids
     */
    private List<Long> driverIds;

    /**
     * 车辆图片
     */
    private String carPic;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆品牌
     */
    private String carBrand;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆颜色
     */
    private String carColor;

    /**
     * 车辆样式
     */
    private String carStyle;

    /**
     * 驱动方式
     */
    private String driveType;

    /**
     * 车辆级别
     */
    private String level;

    /**
     * 车辆识别码（车架号）
     */
    private String vin;

    /**
     * 发动机编号
     */
    private String engine;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 座位数
     */
    private String seat;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

}
