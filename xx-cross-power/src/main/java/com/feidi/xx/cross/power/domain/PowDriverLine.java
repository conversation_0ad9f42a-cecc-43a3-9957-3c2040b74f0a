package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.mybatis.core.domain.BaseCreateEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 司机线路对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_driver_line")
public class PowDriverLine extends BaseCreateEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理ID
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public PowDriverLine() {
    }

    public PowDriverLine(Long agentId, Long driverId, Long lineId) {
        this.agentId = agentId;
        this.driverId = driverId;
        this.lineId = lineId;
    }

}
