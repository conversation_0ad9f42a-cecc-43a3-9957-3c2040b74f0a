package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.cache.power.vo.PowAgentCacheVo;
import com.feidi.xx.cross.common.constant.power.PowerConstants;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.market.api.RemoteInviteConfigService;
import com.feidi.xx.cross.operate.api.RemoteCityService;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.order.api.RemoteAuthService;
import com.feidi.xx.cross.order.api.RemoteRobService;
import com.feidi.xx.cross.order.api.domain.bo.RelationBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteRobVo;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteAgentLineBo;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteLineAgentBo;
import com.feidi.xx.cross.power.domain.*;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentRateVo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.mapper.*;
import com.feidi.xx.cross.power.service.IPowAgentRateService;
import com.feidi.xx.cross.power.service.IPowAgentService;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代理商列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAgentServiceImpl implements IPowAgentService {

    private final IPowAgentRateService powAgentRateService;
    private final PowAgentMapper baseMapper;
    private final PowDriverMapper driverMapper;
    private final PowAgentRateMapper agentRateMapper;
    private final PowAgentLineMapper agentLineMapper;
    private final PowAgentCityMapper agentCityMapper;
    private final PowAgentUserMapper agentUserMapper;
    private final PowDriverLineMapper driverLineMapper;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteRobService remoteRobService;
    @DubboReference
    private final RemoteLineService remoteLineService;
    @DubboReference
    private final RemoteInviteConfigService remoteInviteConfigService;
    @DubboReference
    private final RemoteCityService  remoteCityService;
    @DubboReference
    private final RemoteAuthService remoteAuthService;

    private final IPowAgentUserService powAgentUserService;
    private final PowAgentUserMapper powAgentUserMapper;

    /**
     * 查询代理商列表
     *
     * @param id 主键
     * @return 代理商列表
     */
    @Override
    public PowAgentVo queryById(Long id) {
        PowAgentVo vo = baseMapper.selectVoById(id, PowAgentVo.class);
        //根据代理商id和角色查询代理商用户
        PowAgentUser agentUser = powAgentUserService.queryByAgentId(id);
        //根据代理商id和角色查询代理商用户
        if (ObjectUtil.isNotNull(agentUser)) {
            vo.setName(agentUser.getName());
        }
        vo.setPhone(vo.getLegalPhone());
        // 代理商佣金比例
        List<PowAgentRate> powAgentRates = agentRateMapper.selectByAgentId(id);
        if (CollUtils.isNotEmpty(powAgentRates)) {
            List<PowAgentRateVo> powAgentRateVos = BeanUtils.copyToList(powAgentRates, PowAgentRateVo.class);
            powAgentRateVos.forEach(powAgentRateVo -> powAgentRateVo.setName(PlatformCodeEnum.getInfoByCode(powAgentRateVo.getPlatformCode())));
            vo.setRates(powAgentRateVos);
        }
        return vo;
    }

    @Override
    public List<PowAgentVo> queryChildrenByAgentId(Long agentId) {
        List<PowAgent> powAgents = baseMapper.listByParentId(agentId);
        List<Long> ids = powAgents.stream().map(PowAgent::getId).toList();
        List<PowAgentUser> powAgentUsers = powAgentUserMapper.selectBatchIds(ids);
        if (CollUtil.isNotEmpty(powAgents)) {
            Map<Long, PowAgentUser> powAgentUserMap = powAgentUsers.stream()
                    .collect(Collectors.toMap(PowAgentUser::getAgentId, user -> user));
            List<PowAgentVo> children = new ArrayList<>();
            for (PowAgent agent : powAgents) {
                PowAgentVo tmp = new PowAgentVo();
                tmp.setId(agent.getId());
                tmp.setCompanyName(agent.getCompanyName());
                PowAgentUser user = powAgentUserMap.get(agent.getId());
                if (user != null) {
                    tmp.setName(user.getName());
                    tmp.setPhone(user.getPhone());
                }
                children.add(tmp);
            }
            return children;
        }
        return null;
    }


    /**
     * 查询下级代理商列表
     */
    private List<PowAgent> queryChildren(Long id) {
        if (id == null || id <= 0) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<PowAgent>().eq(PowAgent::getParentId, id));
    }

    /**
     * 分页查询代理商列表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商列表分页列表
     */
    @Override
    public TableDataInfo<PowAgentVo> queryPageList(PowAgentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAgent> lqw = buildQueryWrapper(bo);
        Page<PowAgentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<PowAgentVo> records = result.getRecords();
        fillInfo(records);
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的代理商列表列表
     *
     * @param bo 查询条件
     * @return 代理商列表列表
     */
    @Override
    public List<PowAgentVo> queryList(PowAgentBo bo) {
        LambdaQueryWrapper<PowAgent> lqw = buildQueryWrapper(bo);
        List<PowAgentVo> list = baseMapper.selectVoList(lqw, PowAgentVo.class);
        fillInfo(list);
        return list;
    }

    private void fillInfo(List<PowAgentVo> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<PowAgent> parents = baseMapper.listByParentId(0L);
            Map<Long, PowAgent> agentId2Map = parents.stream().collect(Collectors.toMap(PowAgent::getId, Function.identity()));
            Map<Long, String> parentMap = StreamUtils.toMap(parents, PowAgent::getId, PowAgent::getCompanyName);
            for (PowAgentVo powAgentVo : list) {
                List<PowAgentCity> powAgentCities = agentCityMapper.selectList(new LambdaQueryWrapper<PowAgentCity>().eq(PowAgentCity::getAgentId, powAgentVo.getId()));
                List<Long> cityIds = powAgentCities.stream()
                        .map(pa -> pa.getCityId())
                        .collect(Collectors.toList());
                List<String> strings = remoteCityService.queryByCityIds(cityIds);
                powAgentVo.setCityNames(strings);
                if (agentId2Map.containsKey(powAgentVo.getId())) {
                    powAgentVo.setName(agentId2Map.get(powAgentVo.getId()).getLegalPerson());
                    powAgentVo.setPhone(agentId2Map.get(powAgentVo.getId()).getLegalPhone());
                }

                powAgentVo.setParentAgentName(parentMap.get(powAgentVo.getParentId()));
            }
        }
    }

    private LambdaQueryWrapper<PowAgent> buildQueryWrapper(PowAgentBo bo) {
        LambdaQueryWrapper<PowAgent> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()), PowAgent::getStatus, bo.getStatus());
        if (StringUtils.isNotBlank(bo.getCompanyName())){
            lqw.nested(l ->{
                l.like(PowAgent::getCompanyName, bo.getCompanyName())
                        .or()
                        .like(PowAgent::getTaxNo, bo.getCompanyName());
            });
        }
        lqw.like(ObjectUtil.isNotNull(bo.getTaxNo()), PowAgent::getTaxNo, bo.getTaxNo());
        lqw.like(ObjectUtil.isNotNull(bo.getLegalPerson()), PowAgent::getLegalPerson, bo.getLegalPerson());
        lqw.eq(bo.getProvinceId() != null, PowAgent::getProvinceId, bo.getProvinceId());
        lqw.eq(bo.getCityId() != null, PowAgent::getCityId, bo.getCityId());

        // 时间搜索
        lqw.ge(bo.getStartTime() != null, PowAgent::getCreateTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, PowAgent::getCreateTime, bo.getEndTime());


        UserTypeEnum userType = LoginHelper.getUserType();
        if (UserTypeEnum.AGENT_USER.equals(userType)) {
            // 代理商可以查到自己的信息
            lqw.nested(ObjectUtil.isNotNull(bo.getParentId()), l -> {
                l.eq(PowAgent::getParentId, bo.getParentId())
                        .or().eq(PowAgent::getId, bo.getParentId());
            });
        } else {
            lqw.eq(ObjectUtil.isNotNull(bo.getParentId()) ,PowAgent::getParentId, bo.getParentId());
        }
        return lqw;
    }

    /**
     * 查询符合条件的代理商列表
     *
     * @param bo 查询条件
     * @return 代理商列表
     */
    @Override
    public List<PowAgentVo> queryAllList(PowAgentBo bo) {
        LambdaQueryWrapper<PowAgent> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, PowAgentVo.class);
    }

    /**
     * 新增代理商
     *
     * @param bo 代理商
     * @return 是否新增成功
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(PowAgentBo bo) {
        if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
            PowAgent PowAgent = baseMapper.selectById(bo.getParentId());
            Assert.isTrue(PowAgent.getParentId() == null || PowAgent.getParentId() == 0L, PowerConstants.CHILD_AGENT_UNUSED_MSG);
        }
        validDataUniqueness(bo);
        fillEntityFromParent(bo.getParentId(), bo);
        PowAgent add = MapstructUtils.convert(bo, PowAgent.class);
        validEntityBeforeSave(add);
        add.setStatus(StatusEnum.ENABLE.getCode());
        // 法人手机号做特殊处理
        add.setLegalPhone(bo.getPhone());

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 新增操作 -- 创建账号
            PowAgentUserBo powAgentUserBo = new PowAgentUserBo();
            powAgentUserBo.setAgentId(add.getId());
            powAgentUserBo.setName(add.getLegalPerson());
            powAgentUserBo.setPhone(bo.getPhone());
            powAgentUserBo.setRole(AgentRoleType.ADMIN.getCode());
            powAgentUserService.insertByBo(powAgentUserBo);

            // 代理商佣金比例
            if (CollUtils.isNotEmpty(bo.getRates())) {
                bo.getRates().forEach(agentRateBo -> agentRateBo.setAgentId(add.getId()));
                powAgentRateService.insertByBos(bo.getRates());
            }

            //创建拉新配置
            remoteInviteConfigService.createInviteConfig(add.getId(), add.getCompanyName());

        }
        // 缓存代理商信息
        scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        return flag;
    }


    /**
     * 修改代理商
     *
     * @param bo 代理商
     * @return 是否修改成功
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PowAgentBo bo) {
        if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
            PowAgent PowAgent = baseMapper.selectById(bo.getParentId());
            Assert.isTrue(PowAgent.getParentId() == null || PowAgent.getParentId() == 0L, PowerConstants.CHILD_AGENT_UNUSED_MSG);
        }
        fillEntityFromParent(bo.getParentId(), bo);
        PowAgent update = MapstructUtils.convert(bo, PowAgent.class);
        validEntityBeforeSave(update);
        // 法人手机号做特殊处理
        if (StringUtils.isNotBlank(bo.getPhone())) {
            update.setLegalPhone(bo.getPhone());
        }
        boolean flag = baseMapper.updateById(update) > 0;
        //校验数据唯一性
        validDataUniqueness(bo);
        //更新代理商用户信息
        /*LambdaUpdateWrapper<PowAgentUser> agentUserLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        agentUserLambdaUpdateWrapper.eq(PowAgentUser::getAgentId, update.getId())
                .set(PowAgentUser::getPhone, bo.getPhone())
                .set(PowAgentUser::getName, bo.getName());
        powAgentUserMapper.update(agentUserLambdaUpdateWrapper);*/
        // 更新代理商城市信息
        LambdaUpdateWrapper<PowAgentCity> cityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        cityLambdaUpdateWrapper.eq(PowAgentCity::getAgentId, update.getId())
                .set(PowAgentCity::getCompanyName, update.getCompanyName());
                //.set(PowAgentCity::getCityId, update.getCityId());
        agentCityMapper.update(cityLambdaUpdateWrapper);
        // 更新邀请有奖配置
        remoteInviteConfigService.updateAgentName(update.getId(), update.getCompanyName());
        if (flag) {
            saveRates(bo);
            // 缓存代理商信息
            scheduledExecutorService.schedule(() ->  removeCache(update.getId()), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    private void validDataUniqueness(PowAgentBo bo) {
        Long  taxNoCount = baseMapper.selectCount(new LambdaQueryWrapper<PowAgent>()
                    .ne(ObjectUtil.isNotNull(bo.getId()),PowAgent::getId, bo.getId())
                    .eq(PowAgent::getTaxNo, bo.getTaxNo()));
        if (taxNoCount > 0) {
            throw new ServiceException("该统一社会信用代码已存在");
        }
        Long count = powAgentUserMapper.selectCount(
                new LambdaQueryWrapper<PowAgentUser>()
                        .ne(ObjectUtil.isNotNull(bo.getId()),PowAgentUser::getAgentId, bo.getId())
                        .eq(PowAgentUser::getPhone, bo.getPhone())
        );
        if (count > 0) {
            throw new ServiceException("该手机号已存在");
        }
    }

    private void saveRates(PowAgentBo bo) {
        // 移除旧的
        if (bo.getId() != null && CollUtils.isNotEmpty(bo.getRates())) {
            agentRateMapper.deleteByAgentId(bo.getId());
            bo.getRates().forEach(agentRateBo -> agentRateBo.setAgentId(bo.getId()));
            powAgentRateService.insertByBos(bo.getRates());
        }
    }

    /**
     * 从一级代理商里自动填充二级代理商部分字段
     *
     * @param parentId
     * @param bo
     */
    private void fillEntityFromParent(Long parentId, PowAgentBo bo) {
        if (ObjectUtil.isNotNull(parentId) && parentId > 0) {
            PowAgent parent = baseMapper.selectById(parentId);
            Assert.notNull(parent, "一级代理商不存在");
            // 如果bo相关字段为空则填充省市区相关字段
            if (ObjectUtil.isNull(bo.getProvinceId()) && ObjectUtil.isNotNull(parent.getProvinceId())) {
                bo.setProvinceId(parent.getProvinceId());
            }
            if (ObjectUtil.isNull(bo.getCityId()) && ObjectUtil.isNotNull(parent.getCityId())) {
                bo.setCityId(parent.getCityId());
            }
            if (ObjectUtil.isNull(bo.getDistrictId()) && ObjectUtil.isNotNull(parent.getDistrictId())) {
                bo.setDistrictId(parent.getDistrictId());
            }
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAgent entity) {
        Assert.isTrue(StrUtil.isAllNotBlank(entity.getCompanyName(), entity.getTaxNo()), "企业名称和信用代码不能为空");
        if (ObjectUtil.isNotNull(entity.getId())) {
            PowAgent powAgent = baseMapper.selectById(entity.getId());
            //entity中的主体名称和社会通义代码证对比对应数据库中的主体名称和统一社会信用代码
            if (!entity.getCompanyName().equals(powAgent.getCompanyName()) || !entity.getTaxNo().equals(powAgent.getTaxNo())) {
                boolean existsByCompany = baseMapper.existsByCompany(entity.getCompanyName(), entity.getTaxNo());
                Assert.isTrue(!existsByCompany, "该主体已注册代理商，请修改主体名称和统一社会信用代码");
            }
        } else {
            boolean existsByCompany = baseMapper.existsByCompany(entity.getCompanyName(), entity.getTaxNo());
            Assert.isTrue(!existsByCompany, "该主体已注册代理商，请修改主体名称和统一社会信用代码");
            Assert.isTrue(ObjectUtils.isNotEmpty(entity.getServicesPhone()), "客服电话不能为空!");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateStatus(Long id, String status) {
        PowAgent powAgent = baseMapper.selectById(id);
        if (!powAgent.getStatus().equals(status) && StatusEnum.getInfoByCode(status) != null) {
            powAgent.setStatus(status);
            boolean ret = baseMapper.updateById(powAgent) > 0;
            if (ret && StatusEnum.DISABLE.getCode().equals(status)) {
                disableAgent(id);
                //下线
                String loginId = LoginHelper.getLoginId(UserTypeEnum.AGENT_USER.getUserType(), id);
                remoteAuthService.logout(loginId);
            } else if (ret && StatusEnum.ENABLE.getCode().equals(status)) {
                enableAgent(id);
            }
            return ret;
        }
        return true;
    }

    /**
     * 校验并批量删除代理商信息
     *  TODO 验证是否：
     *      有二级代理商（禁用|线路清空|司机自动抢单禁用）、
     *      有司机（司机必须禁用|司机自动抢单禁用）、
     *      有线路（清空|下面的二级代理商线路清空|司机线路清空）、
     *      有自动抢单（禁用）
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            for (Long id : ids) {
                disableAgent(id);
            }
        }
        // 删除缓存信息
        scheduledExecutorService.schedule(() -> ids.forEach(this::removeCache), 0, TimeUnit.SECONDS);
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableAgent(Long agentId) {
        baseMapper.enableAgent(agentId);
        // 启用司机
        List<PowDriver> powDrivers = driverMapper.listByAgentId(agentId);
        if (CollUtil.isNotEmpty(powDrivers)) {
            powDrivers.forEach(e -> e.setStatus(StatusEnum.ENABLE.getCode()));
            driverMapper.updateBatchById(powDrivers);
        }
        //启用代理商用户
        LambdaUpdateWrapper<PowAgentUser> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(PowAgentUser::getAgentId, agentId).eq(PowAgentUser::getStatus, StatusEnum.DISABLE.getCode()).set(PowAgentUser::getStatus, StatusEnum.ENABLE.getCode());
        powAgentUserMapper.update(lqw);
        // 启用二级代理
        List<PowAgent> children = queryChildren(agentId);
        for (PowAgent child : children) {
            enableAgent(child.getId());
        }
    }

    /**
     * 禁用代理商线路的后续操作
     *
     * @param agentId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public void disableAgent(long agentId) {
        agentLineMapper.deleteByAgentId(agentId);
        driverLineMapper.deleteByAgentId(agentId);
        remoteRobService.disableAgentRob(agentId);
        // 禁用司机，抢单禁用在代理抢单禁用时一起禁用了
        List<PowDriver> powDrivers = driverMapper.listByAgentId(agentId);
        if (CollUtil.isNotEmpty(powDrivers)) {
            powDrivers.forEach(e -> e.setStatus(StatusEnum.DISABLE.getCode()));
            driverMapper.updateBatchById(powDrivers);
        }
        //禁用代理商及用户并下线
        LambdaUpdateWrapper<PowAgentUser> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(PowAgentUser::getAgentId, agentId).set(PowAgentUser::getStatus, StatusEnum.DISABLE.getCode());
        boolean ret = powAgentUserMapper.update(lqw) > 0;
        if (ret){
            List<PowAgentUser> powAgentUsers = powAgentUserMapper.selectList(lqw);
            for (PowAgentUser powAgentUser : powAgentUsers){
                String loginId = LoginHelper.getLoginId(UserTypeEnum.AGENT_USER.getUserType(), powAgentUser.getId());
                remoteAuthService.logout(loginId);
            }
        }
        // 禁用二级代理
        List<PowAgent> children = queryChildren(agentId);
        for (PowAgent child : children) {
            disableAgent(child.getId());
        }
    }

    /**
     * 禁用代理商下的某些线路
     *
     * @param agentId
     * @param lineIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public void disableLineOfAgent(Long agentId, List<Long> lineIds) {
        if (CollUtils.isEmpty(lineIds)) {
            // 没有要删除的不删除
            return;
        }
        // 禁用抢单
        RelationBo bo = new RelationBo();
        bo.setAgentIds(Arrays.asList(agentId));
        bo.setLineIds(lineIds);
        remoteRobService.disableRob(bo);
        // 移除司机下的线路
        driverLineMapper.deleteByLineIds(agentId, lineIds);
        // 移除代理下的线路
        agentLineMapper.deleteByLineIds(agentId, lineIds);

        List<PowAgent> children = queryChildren(agentId);
        for (PowAgent child : children) {
            disableLineOfAgent(child.getId(), lineIds);
        }
    }

    /**
     * 禁用线路下的某些代理商
     *
     * @param lineId
     * @param agentIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public void disableAgentOfLine(Long lineId, List<Long> agentIds) {
        if (CollUtils.isEmpty(agentIds)) {
            // 没有要删除的不删除
            return;
        }
        // 禁用抢单
        RelationBo bo = new RelationBo();
        bo.setAgentIds(agentIds);
        bo.setLineIds(Arrays.asList(lineId));
        //todo 订单处理后续进行
        //remoteRobService.disableRob(bo);
        // 移除司机下的线路
        driverLineMapper.deleteByAgentIds(lineId, agentIds);
        // 移除代理下的线路
        agentLineMapper.deleteByAgentIds(lineId, agentIds);
    }

    /**
     * 生成邀请码
     *
     * @return
     */
    private String makeCode() {
        String code = RandomUtil.randomString(8);
        if (ObjectUtils.isNotNull(agentUserMapper.queryByCode(code))) {
            makeCode();
        }
        return code;
    }

    /**
     * 给二级代理分配线路
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean assignLine2child(RemoteAgentLineBo bo) {
        PowAgent powAgent = baseMapper.selectById(bo.getParentId());
        Assert.isTrue(powAgent.getParentId() == null || powAgent.getParentId() == 0L, PowerConstants.CHILD_AGENT_UNUSED_MSG);
        List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(bo.getParentId());
        Set<Long> set = StreamUtils.toSet(agentLines, PowAgentLine::getLineId);
        Assert.isTrue(set.containsAll(bo.getLineIds()), "只能分配已有的线路");
        set.retainAll(bo.getLineIds());

        List<RemoteLineVo> lineVo = remoteLineService.queryByLineIds(bo.getLineIds());
        Map<Long, String> lineMap = StreamUtils.toMap(lineVo, RemoteLineVo::getId, RemoteLineVo::getName);

        List<RemoteRobVo> robVos = remoteRobService.listByAgentIdAndLineId(bo.getParentId(), null);
        Map<Long, RemoteRobVo> robMap = StreamUtils.toMap(robVos, RemoteRobVo::getLineId, Function.identity());

        List<PowDriverLine> driverLines = driverLineMapper.listByLineIds(bo.getParentId(), bo.getLineIds());
        Map<Long, List<PowDriverLine>> lineDriverMap = StreamUtils.groupByKey(driverLines, PowDriverLine::getLineId);

        List<PowAgentLine> existAgentLines = agentLineMapper.listByParentId(bo.getParentId());
        Map<Long, Long> lineAgentMap = existAgentLines.stream()
                .collect(Collectors.toMap(PowAgentLine::getLineId, PowAgentLine::getAgentId, (o1, o2) -> o1));

        for (Long lineId : set) {
            String lineName = lineMap.get(lineId);
            Long existAgent = lineAgentMap.get(lineId);
            // 主代理商分配给其他子代理商的要禁用
            if (existAgent != null && !existAgent.equals(bo.getAgentId())) {
//                Assert.isNull(existAgent, StrUtil.format("线路【{}】已分配，无法操作", lineName));
                disableLineOfAgent(existAgent, Arrays.asList(lineId));
            }

            // 未设置抢单
            Assert.isNull(robMap.get(lineId), StrUtil.format("线路【{}】已设置抢单，无法操作", lineName));
            // 未分配给司机
            List<PowDriverLine> drivers = lineDriverMap.get(lineId);
            Assert.isTrue(CollUtil.isEmpty(drivers), StrUtil.format("线路【{}】已分配司机，无法操作", lineName));
        }
        return assignLine(bo);
    }

    @Override
    public List<Long> listByAgentId(Long agentId) {
        List<PowAgentLine> agentLines = agentLineMapper.listByAgentId(agentId);
        return agentLines.stream().map(PowAgentLine::getLineId).toList();
    }

    @Override
    public List<Long> listByLineId(Long lineId) {
        List<PowAgentLine> agentLines = agentLineMapper.listByLineId(lineId);
        return agentLines.stream().map(PowAgentLine::getAgentId).toList();
    }

    /**
     * 给代理商分配线路
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean assignLine(RemoteAgentLineBo bo) {
        if (UserTypeEnum.SYS_USER.equals(LoginHelper.getUserType())) {
            PowAgent agent = baseMapper.selectById(bo.getAgentId());
            Assert.isTrue(ArithUtils.isNull(agent.getParentId()), "请勿直接给子代理商分配线路");
        }

        if (CollUtil.isNotEmpty(bo.getLineIds())) {
            List<PowAgentLine> agentLines = agentLineMapper.listByLineIds(bo.getLineIds());
            Map<Long, Long> map = agentLines.stream().collect(Collectors.toMap(PowAgentLine::getLineId, PowAgentLine::getAgentId, (a, b) -> a));
            // 查看线路是否已有代理商
            UserTypeEnum userType = LoginHelper.getUserType();
            for (Long lineId : bo.getLineIds()) {
                Long agentId = map.get(lineId);
                if (UserTypeEnum.SYS_USER.equals(userType)) {
                    Assert.isTrue(bo.getAgentId().equals(agentId) || agentId == null, "已设置代理商的线路无法再次分配");
                } else if (UserTypeEnum.AGENT_USER.equals(userType)) {
                    Assert.isTrue(bo.getParentId().equals(agentId) || agentId == null, "已设置代理商的线路无法再次分配");
                }
            }
        }

        // 原来的线路
        List<PowAgentLine> oldLines = agentLineMapper.listByAgentId(bo.getAgentId());
        List<Long> oldLineIds = StreamUtils.toList(oldLines, PowAgentLine::getLineId);
        if (CollUtils.isNotEmpty(oldLineIds)) {
            // 删掉当前代理商及其子代理商的部分线路，未删除的保留
            Set<Long> delLines = CollUtils.getDiff(oldLineIds, bo.getLineIds());
            if (CollUtil.isNotEmpty(delLines)) {
                disableLineOfAgent(bo.getAgentId(), new ArrayList<>(delLines));
            }
        }
        // 新的线路
        Set<Long> addLines = CollUtils.getDiff(bo.getLineIds(), oldLineIds);
        if (CollUtil.isNotEmpty(addLines)) {
            List<PowAgent> agents = baseMapper.selectList();
            Map<Long, PowAgent> parentMap = agents.stream().collect(Collectors.toMap(PowAgent::getId, Function.identity()));

            List<PowAgentLine> ret = new ArrayList<>();
            for (Long id : addLines) {
                Long parentId = parentMap.getOrDefault(bo.getAgentId(), new PowAgent()).getParentId();
                PowAgentLine agentLine = new PowAgentLine(bo.getAgentId(), parentId, id);
                ret.add(agentLine);
            }
            return agentLineMapper.insertBatch(ret);
        }
        return true;
    }

    /**
     * 给线路分配代理商
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean assignAgent(RemoteLineAgentBo bo) {
        Assert.isTrue(bo.getAgentIds().size() <= 1, "一条线路只能分配给一个代理商");

        if (UserTypeEnum.SYS_USER.equals(LoginHelper.getUserType())) {
            if (CollUtil.isNotEmpty(bo.getAgentIds())) {
                for (Long agentId : bo.getAgentIds()) {
                    PowAgent agent = baseMapper.selectById(agentId);
                    Assert.isTrue(ArithUtils.isNull(agent.getParentId()), "请勿直接给子代理商分配线路");
                }
            }
        }

        // 查看线路是否已有代理商，有的话把之前的代理商禁用
        List<PowAgentLine> agentLines = agentLineMapper.listByLineId(bo.getLineId());
        agentLines = agentLines.stream().filter(e -> !CollUtil.contains(bo.getAgentIds(), e.getAgentId())).toList();
        if (CollUtil.isNotEmpty(agentLines)) {
            List<Long> existAgentIds = StreamUtils.toList(agentLines, PowAgentLine::getAgentId);
            disableAgentOfLine(bo.getLineId(), existAgentIds);
        }

        // 清空线路下的代理商
        agentLineMapper.deleteByLineId(bo.getLineId());
        if (CollUtil.isNotEmpty(bo.getAgentIds())) {
            List<PowAgentLine> ret = new ArrayList<>();
            List<PowAgent> agents = baseMapper.selectList();
            Map<Long, PowAgent> parentMap = agents.stream().collect(Collectors.toMap(PowAgent::getId, Function.identity()));
            for (Long agentId : bo.getAgentIds()) {
                Long parentId = parentMap.getOrDefault(agentId, new PowAgent()).getParentId();
                PowAgentLine tmp = new PowAgentLine(agentId, parentId, bo.getLineId());
                ret.add(tmp);
            }
            return agentLineMapper.insertBatch(ret);
        }
        return true;
    }

    /**
     * 根据代理商ID查询代理信息，并缓存
     *
     * @param id 代理商ID
     * @return 代理信息
     */
    @Override
    public PowAgent queryByIdAndCacheInfo(Long id) {
        PowAgent powAgent = baseMapper.selectById(id);
        scheduledExecutorService.schedule(() -> addCache(powAgent), 0, TimeUnit.SECONDS);
        return powAgent;
    }

    /**
     * 根据代理商ID查询代理信息，并缓存
     *
     * @param ids 代理商ID集合
     * @return 代理信息
     */
    @Override
    public List<PowAgent> queryByIdsAndCacheInfo(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<PowAgent> powAgents = baseMapper.selectBatchIds(ids);

        // 添加缓存
        scheduledExecutorService.schedule(() -> powAgents.forEach(this::addCache), 0, TimeUnit.SECONDS);
        return powAgents;
    }

    /**
     * 缓存代理商信息
     *
     * @param agent 代理商信息
     */
    public void addCache(PowAgent agent) {
        if (agent != null) {
            String carKey = PowCacheKeyEnum.POW_AGENT_INFO_CACHE_KEY.create(agent.getId());
            RedisUtils.setCacheObject(carKey, BeanUtils.copyProperties(agent, PowAgentCacheVo.class), PowCacheKeyEnum.POW_AGENT_INFO_CACHE_KEY.getDuration());
        }
    }

    /**
     * 删除缓存
     *
     * @param id 代理商ID
     */
    public void removeCache(Long id) {
        RedisUtils.deleteObject(PowCacheKeyEnum.POW_AGENT_INFO_CACHE_KEY.create(id));
    }

}
