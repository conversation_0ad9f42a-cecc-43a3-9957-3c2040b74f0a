package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemoteAgentRateVo;
import com.feidi.xx.cross.power.domain.PowAgentRate;
import com.feidi.xx.cross.power.domain.bo.PowAgentRateBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentRateVo;
import com.feidi.xx.cross.power.mapper.PowAgentRateMapper;
import com.feidi.xx.cross.power.service.IPowAgentRateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 代理商佣金比例Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAgentRateServiceImpl implements IPowAgentRateService {

    private final PowAgentRateMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;


    /**
     * 查询代理商佣金比例
     *
     * @param id 主键
     * @return 代理商佣金比例
     */
    @Override
    public PowAgentRateVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询代理商佣金比例列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商佣金比例分页列表
     */
    @Override
    public TableDataInfo<PowAgentRateVo> queryPageList(PowAgentRateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAgentRate> lqw = buildQueryWrapper(bo);
        Page<PowAgentRateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代理商佣金比例列表
     *
     * @param bo 查询条件
     * @return 代理商佣金比例列表
     */
    @Override
    public List<PowAgentRateVo> queryList(PowAgentRateBo bo) {
        LambdaQueryWrapper<PowAgentRate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowAgentRate> buildQueryWrapper(PowAgentRateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowAgentRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), PowAgentRate::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getAgentId() != null, PowAgentRate::getAgentId, bo.getAgentId());
        lqw.eq(bo.getRate() != null, PowAgentRate::getRate, bo.getRate());
        return lqw;
    }

    /**
     * 新增代理商佣金比例
     *
     * @param bo 代理商佣金比例
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowAgentRateBo bo) {
        PowAgentRate add = MapstructUtils.convert(bo, PowAgentRate.class);
        validEntityBeforeSave(add);
        if (bo.getPlatformCode() !=null){
            add.setPlatformCode(PlatformCodeEnum.TY.getCode());
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBos(List<PowAgentRateBo> bos) {
        if (CollUtil.isEmpty(bos)) {
            return false;
        }
        bos.forEach(e -> {
            PowAgentRate agentRate = BeanUtils.copyProperties(e, PowAgentRate.class);
            baseMapper.insert(agentRate);
            scheduledExecutorService.schedule(() -> addCache(agentRate), 0, TimeUnit.SECONDS);
        });
        return true;
    }

    /**
     * 修改代理商佣金比例
     *
     * @param bo 代理商佣金比例
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowAgentRateBo bo) {
        PowAgentRate update = MapstructUtils.convert(bo, PowAgentRate.class);
        validEntityBeforeSave(update);
        if (bo.getPlatformCode() !=null){
            update.setPlatformCode(PlatformCodeEnum.TY.getCode());
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAgentRate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代理商佣金比例信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
    /**
     * 添加缓存
     *
     * @param agentRate 代理商佣金比例
     */
    private void addCache(PowAgentRate agentRate) {
        if (agentRate == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = PowCacheKeyEnum.POW_AGENT_RATE_CACHE_KEY.create(agentRate.getAgentId(), agentRate.getPlatformCode());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(agentRate, RemoteAgentRateVo.class),
                PowCacheKeyEnum.POW_AGENT_RATE_CACHE_KEY.getDuration());
    }

    /**
     * 删除缓存
     *@param platformCode
     * @param agentId
     */
    private void deleteCache(Long agentId,String platformCode) {
        RedisUtils.deleteObject(PowCacheKeyEnum.POW_AGENT_RATE_CACHE_KEY.create(agentId,platformCode));
    }
}
