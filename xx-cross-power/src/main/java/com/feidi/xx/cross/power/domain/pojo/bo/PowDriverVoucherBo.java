package com.feidi.xx.cross.power.domain.pojo.bo;

import com.feidi.xx.common.core.enums.PtLoginTypeEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.power.domain.PowDriverVoucher;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 司机凭证业务对象 Pow_driver_voucher
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriverVoucher.class, reverseConvertGenerate = false)
public class PowDriverVoucherBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 类型
     */
    private PtLoginTypeEnum type;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * union_id
     */
    private String unionId;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

}
