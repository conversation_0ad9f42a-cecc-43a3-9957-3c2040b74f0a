package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.feidi.xx.cross.power.domain.vo.PowDriverRateVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.List;

/**
 * 司机佣金比例Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface PowDriverRateMapper extends BaseMapperPlus<PowDriverRate, PowDriverRateVo> {

    /**
     * 根据司机id更新佣金比例
     */
    default int updateRate(Long driverId, BigDecimal rate) {
        return update(new LambdaUpdateWrapper<PowDriverRate>()
                .set(PowDriverRate::getRate, rate)
                .eq(PowDriverRate::getDriverId, driverId));
    }

    /**
     * 根据司机id获取佣金比例信息
     */
    default PowDriverRate queryByDriverId(Long driverId) {
        return selectOne(new LambdaUpdateWrapper<PowDriverRate>()
                .eq(PowDriverRate::getDriverId, driverId));
    }

    /**
     * 根据ids获取佣金比例信息
     */
    default List<PowDriverRate> queryByDriverIds(List<Long> ids) {
        return selectList(new LambdaUpdateWrapper<PowDriverRate>()
                .in(PowDriverRate::getDriverId, ids));
    }

}
