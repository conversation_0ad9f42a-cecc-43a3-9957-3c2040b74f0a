package com.feidi.xx.cross.power.util;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.URLUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Iterator;

/**
 * <AUTHOR>
 */
@Slf4j
public class ImgCompress {

    // 最大尝试次数
    private static final int MAX_TRY_COUNT = 30;
    // 可接受的大小误差范围（字节）
    private static final int ACCEPTABLE_ERROR = 0;
    //
    private static final int UNIT_KB = 1024;

    /**
     * 将图片压缩到指定大小
     * @param targetSize 目标大小（KB）
     * @return 是否成功压缩到指定大小范围内
     * @throws Exception 可能抛出的异常
     */
    public static String compressImageToSize(String name, String imgUrl, long targetSize) throws Exception {
        log.info("imgUrl:{}", imgUrl);

        OutputStream out = new ByteArrayOutputStream();
        ImgUtil.scale(ImgUtil.read(URLUtil.url(imgUrl)), out,0.05f);

        ByteArrayOutputStream byteArrayOutputStream = (ByteArrayOutputStream) out;
        byte[] byteArray = byteArrayOutputStream.toByteArray();

        // 将字节数组转换为 Base64 编码字符串
        return java.util.Base64.getEncoder().encodeToString(byteArray);


        /*targetSize = targetSize * UNIT_KB;
        // 读取原始图片
        BufferedImage image = ImgUtil.read(URLUtil.url(imgUrl));
        //File inputFile = new File(inputImagePath);
        //BufferedImage image = ImageIO.read(inputFile);
        log.info("image:{}", JSONUtil.toJsonStr(image));
        // 获取文件格式
        String formatName = getFileFormat(name);
        log.info("format: {}", formatName);
        // 如果原始图片已经小于目标大小，直接复制
        if (image.getData().getDataBuffer().getSize() <= targetSize) {
            //ImageIO.write(image, formatName, new File(outputImagePath));
            //return Base64.encode(image.toString());
        }

        // 使用二分法寻找合适的压缩质量
        float low = 0.0f;
        float high = 1.0f;
        float quality = 0.5f;

        byte[] bestResult = null;
        int tryCount = 0;

        while (tryCount < MAX_TRY_COUNT) {
            // 按当前质量压缩并获取字节数组
            byte[] compressedData = compressImageToBytes(image, formatName, quality);

            // 检查大小是否符合要求
            if (getImageFileSize(convert(compressedData), formatName) <= targetSize + ACCEPTABLE_ERROR) {
                // 如果在可接受范围内，尝试提高质量以获得更好的图片
                bestResult = compressedData;
                low = quality;
            } else {
                // 如果太大，降低质量
                high = quality;
            }

            // 计算新的质量参数
            quality = (low + high) / 2;
            tryCount++;

            // 如果已经找到合适的结果且质量参数变化不大，提前退出
            if (bestResult != null && Math.abs(high - low) < 0.01) {
                break;
            }
        }

        // 保存最佳结果
        if (bestResult != null) {
            *//*try (OutputStream out = new FileOutputStream(outputImagePath)) {
                out.write(bestResult);
            }*//*
            return Base64.encode(bestResult);
            //return convert(bestResult).getData().getDataBuffer().getSize() <= targetSize + ACCEPTABLE_ERROR;
        }

        // 如果找不到合适的质量参数，尝试最后一次压缩
        byte[] finalData = compressImageToBytes(image, formatName, 0.05f);
        *//*try (OutputStream out = new FileOutputStream(outputImagePath)) {
            out.write(finalData);
        }*//*
        return Base64.encode(finalData);*/

        //return convert(finalData).getData().getDataBuffer().getSize() <= targetSize + ACCEPTABLE_ERROR;
    }

    /**
     * 将图片按指定质量压缩为字节数组
     */
    private static byte[] compressImageToBytes(BufferedImage image, String formatName, float quality) throws Exception {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) {
                throw new Exception("找不到支持的图片格式: " + formatName);
            }

            ImageWriter writer = writers.next();
            ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(byteArrayOutputStream);
            writer.setOutput(imageOutputStream);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(quality);
            }

            // 写入图片
            writer.write(null, new IIOImage(image, null, null), param);

            // 清理资源
            imageOutputStream.flush();
            writer.dispose();
            imageOutputStream.close();

            return byteArrayOutputStream.toByteArray();
        }
    }

    /**
     * 从文件路径获取图片格式
     */
    private static String getFileFormat(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        // 默认格式
        return "jpg";
    }

    public static BufferedImage convert(byte[] imageBytes) throws IOException {
        if (imageBytes == null || imageBytes.length == 0) {
            throw new IllegalArgumentException("字节数组不能为null或空");
        }

        // 将字节数组转换为输入流
        try (InputStream inputStream = new ByteArrayInputStream(imageBytes)) {
            // 读取输入流并转换为BufferedImage
            BufferedImage bufferedImage = ImageIO.read(inputStream);

            if (bufferedImage == null) {
                throw new IOException("无法识别的图片格式或无效的图片数据");
            }

            // 返回Image对象（BufferedImage是Image的子类）
            return bufferedImage;
        }
    }

    public static long getImageFileSize(BufferedImage image, String formatName) throws IOException {
        // 创建字节输出流
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 将图像写入字节流
            ImageIO.write(image, formatName, baos);
            // 返回字节流的大小，即文件大小（字节）
            return baos.size();
        }
    }
}
