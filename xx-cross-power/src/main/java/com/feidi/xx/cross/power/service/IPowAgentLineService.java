package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowAgentLineVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentLineBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 代理商线路Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAgentLineService {

    /**
     * 查询代理商线路
     *
     * @param id 主键
     * @return 代理商线路
     */
    PowAgentLineVo queryById(Long id);

    /**
     * 分页查询代理商线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商线路分页列表
     */
    TableDataInfo<PowAgentLineVo> queryPageList(PowAgentLineBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代理商线路列表
     *
     * @param bo 查询条件
     * @return 代理商线路列表
     */
    List<PowAgentLineVo> queryList(PowAgentLineBo bo);

    /**
     * 新增代理商线路
     *
     * @param bo 代理商线路
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAgentLineBo bo);

    /**
     * 修改代理商线路
     *
     * @param bo 代理商线路
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAgentLineBo bo);

    /**
     * 校验并批量删除代理商线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据代理商id查询线路id
     *
     * @param id 代理商id
     * @return 线路id集合
     */
    List<Long> listByAgentId(@NotNull(message = "主键不能为空") Long id);
}
