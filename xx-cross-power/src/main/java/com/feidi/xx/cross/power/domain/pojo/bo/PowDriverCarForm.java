package com.feidi.xx.cross.power.domain.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.power.validate.ReviewGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 司机加盟身份信息业务对象
 */
@Data
public class PowDriverCarForm implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车辆所有人
     */
    @NotBlank(message = "车辆所有人不能为空", groups = {ReviewGroup.class})
    private String carOwner;

    /**
     * 车牌号
     */
    @NotBlank(message ="车牌不能为空", groups = {ReviewGroup.class})
    private String carNumber;

    /**
     * 车辆品牌
     */
    @NotBlank(message = "车辆品牌不能为空", groups = {ReviewGroup.class})
    private String carBrand;

    /**
     * 车辆型号
     */
    @NotBlank(message = "车辆型号不能为空", groups = {ReviewGroup.class})
    private String carModel;

    /**
     * 车辆颜色
     */
    @NotBlank(message = "车辆颜色不能为空", groups = {ReviewGroup.class})
    private String carColor;

    /**
     * 能源类型[UpEnergyTypeEnum]
     */
    @NotBlank(message = "能源类型不能为空", groups = {ReviewGroup.class})
    private String driveType;

    /**
     * 核载人数
     */
    @NotBlank(message = "核载人数不能为空", groups = {ReviewGroup.class})
    private String seat;

    /**
     * 注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "注册日期不能为空", groups = {ReviewGroup.class})
    private Date firstTime;

    /**
     * 发证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 类型
     */
    private String carStyle;

    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 发动机代号
     */
    private String engine;

    /**
     * 车辆照片
     */
    @NotNull(message = "车辆照片不能为空", groups = {ReviewGroup.class})
    private Long carOssId;

    /**
     * 行驶证(正页)
     */
    @NotNull(message = "行驶证(正页)不能为空", groups = {ReviewGroup.class})
    private Long travelFrontOssId;

    /**
     * 行驶证(副页)
     */
    @NotNull(message = "行驶证(副页)不能为空", groups = {ReviewGroup.class})
    private Long travelBackOssId;

    /**
     * 车辆保单
     */
    @NotNull(message = "车辆保单不能为空", groups = {ReviewGroup.class})
    private Long insuranceOssId;

    /**
     * 驳回原因
     */
    private List<String> reasons;
}
