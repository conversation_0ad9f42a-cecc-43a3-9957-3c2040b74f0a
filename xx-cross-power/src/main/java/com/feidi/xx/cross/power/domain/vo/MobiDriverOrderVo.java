package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 移动端-调度 司机订单
 * <AUTHOR>
 */
@Data
@Builder
public class MobiDriverOrderVo implements Serializable {
    /**
     * 司机id
     */
    private Long driverId;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 订单数
     */
    private Long orderNumber;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 订单信息
     */
    private List<RemoteOrderVo> remoteOrderVos;

}
