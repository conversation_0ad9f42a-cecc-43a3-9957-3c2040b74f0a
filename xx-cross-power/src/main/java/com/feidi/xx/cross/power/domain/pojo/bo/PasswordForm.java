package com.feidi.xx.cross.power.domain.pojo.bo;

import com.feidi.xx.common.core.constant.RegexConstants;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户密码修改bo
 */
@Data
public class PasswordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 旧密码
     */
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = RegexConstants.SIMPLE_PASSWORD, message = "新密码必须包含字母和数字，长度不少于8位")
    private String newPassword;
}
