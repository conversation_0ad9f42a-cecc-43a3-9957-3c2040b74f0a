package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.PowDriverVoucher;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverVoucherBo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverVoucherVo;
import com.feidi.xx.cross.power.mapper.PowDriverVoucherMapper;
import com.feidi.xx.cross.power.service.IPowDriverVoucherService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 司机凭证Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@RequiredArgsConstructor
@Service
public class PowDriverVoucherServiceImpl implements IPowDriverVoucherService {

    private final PowDriverVoucherMapper baseMapper;

    /**
     * 查询司机凭证
     *
     * @param id 主键
     * @return 司机凭证
     */
    @Override
    public PowDriverVoucherVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机凭证列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机凭证分页列表
     */
    @Override
    public TableDataInfo<PowDriverVoucherVo> queryPageList(PowDriverVoucherBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverVoucher> lqw = buildQueryWrapper(bo);
        Page<PowDriverVoucherVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机凭证列表
     *
     * @param bo 查询条件
     * @return 司机凭证列表
     */
    @Override
    public List<PowDriverVoucherVo> queryList(PowDriverVoucherBo bo) {
        LambdaQueryWrapper<PowDriverVoucher> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowDriverVoucher> buildQueryWrapper(PowDriverVoucherBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverVoucher> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowDriverVoucher::getDriverId, bo.getDriverId());
        lqw.eq(ObjectUtils.isNotNull(bo.getType()), PowDriverVoucher::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), PowDriverVoucher::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenId()), PowDriverVoucher::getOpenId, bo.getOpenId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnionId()), PowDriverVoucher::getUnionId, bo.getUnionId());
        lqw.eq(ObjectUtils.isNotNull(bo.getStatus()), PowDriverVoucher::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增司机凭证
     *
     * @param bo 司机凭证
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowDriverVoucherBo bo) {
        PowDriverVoucher add = MapstructUtils.convert(bo, PowDriverVoucher.class);
        PowDriverVoucher exist = exist(bo.getDriverId(), bo.getType().getCode());
        if (exist == null) {
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            }
            return flag;
        } else {
            // 更新openid之类的新值
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreNullValue(true);
            BeanUtil.copyProperties(add, exist, copyOptions);
            baseMapper.updateById(exist);
        }
        return true;
    }

    /**
     * 修改司机凭证
     *
     * @param bo 司机凭证
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowDriverVoucherBo bo) {
        PowDriverVoucher update = MapstructUtils.convert(bo, PowDriverVoucher.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private boolean validEntityBeforeSave(PowDriverVoucher entity){
        return true;
    }

    /**
     * 校验并批量删除司机凭证信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public PowDriverVoucher exist(Long driverId, String loginType) {
        PowDriverVoucher voucher = baseMapper.selectOne(Wrappers.<PowDriverVoucher>lambdaQuery()
                .eq(PowDriverVoucher::getDriverId, driverId)
                .eq(PowDriverVoucher::getType, loginType));
        return voucher;
    }

    @Override
    public String getOpenId(Long driverId) {
        LambdaQueryWrapper<PowDriverVoucher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PowDriverVoucher::getDriverId, driverId)
//                .eq(PowDriverVoucher::getStatus, StatusEnum.ENABLE.getCode())
//                .eq(PowDriverVoucher::getType, PtLoginTypeEnum.WX_MINI_APP.getCode())
                .isNotNull(PowDriverVoucher::getOpenId)
                //.orderByDesc(PowDriverVoucher::getUpdateTime)
                .last("LIMIT 1");
        PowDriverVoucher voucher = baseMapper.selectOne(queryWrapper);
        if (voucher == null) {
            return null;
        }
        return voucher.getOpenId();
    }
}
