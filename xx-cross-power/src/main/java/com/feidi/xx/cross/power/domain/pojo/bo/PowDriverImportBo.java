package com.feidi.xx.cross.power.domain.pojo.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.domain.PowDriver;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

@Data
@AutoMappers({
    @AutoMapper(target = PowDriver.class, reverseConvertGenerate = false),
    @AutoMapper(target = PowCar.class, reverseConvertGenerate = false)
})
public class PowDriverImportBo {

    /**
     * 代理商名称
     */
    @ExcelProperty(index = 0)
    private String agentName;

    /**
     * 司机姓名
     */
    @ExcelProperty(index = 1)
    private String name;

    /**
     * 司机手机号
     */
    @ExcelProperty(index = 2)
    private String phone;

    /**
     * 身份证号
     */
    @ExcelProperty(index = 3)
    private String cardNo;

    /**
     * 司机性别
     */
    @ExcelProperty(index = 4)
    private String sex;

    /**
     * 密码
     */
    @ExcelProperty(index = 5)
    private String capitalPassword;

    /**
     * 佣金比例
     */
    @ExcelProperty(index = 6)
    private Integer rate;

    /**
     * 邀请码
     */
    @ExcelProperty(index = 7)
    private String code;

    /**
     * 司机姓名
     */
    @ExcelProperty(index = 8)
    private String aliName;

    /**
     * 司机手机号
     */
    @ExcelProperty(index = 9)
    private String aliPhone;

    /**
     * 账号/邮箱
     */
    @ExcelProperty(index = 10)
    private String account;

    /**
     * 车牌号
     */
    @ExcelProperty(index = 11)
    private String carNumber;

    /**
     * 车辆品牌
     */
    @ExcelProperty(index = 12)
    private String carBrand;

    /**
     * 车辆型号
     */
    @ExcelProperty(index = 13)
    private String carModel;

    /**
     * 车辆颜色
     */
    @ExcelProperty(index = 14)
    private String carColor;

    /**
     * 车架号
     */
    @ExcelProperty(index = 15)
    private String vin;

    /**
     * 发动机号
     */
    @ExcelProperty(index = 16)
    private String engine;


}
