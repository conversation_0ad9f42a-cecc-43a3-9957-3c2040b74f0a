package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.core.enums.PtLoginTypeEnum;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 司机凭证对象 Pow_driver_voucher
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("Pow_driver_voucher")
public class PowDriverVoucher extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 类型
     */
    private PtLoginTypeEnum type;

    /**
     * app_id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String appId;

    /**
     * open_id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String openId;

    /**
     * union_id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String unionId;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
