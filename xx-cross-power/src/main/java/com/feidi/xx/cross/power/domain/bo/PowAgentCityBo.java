package com.feidi.xx.cross.power.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.power.domain.PowAgentCity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理商城市业务对象 pow_agent_city
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAgentCity.class, reverseConvertGenerate = false)
public class PowAgentCityBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String companyName;

    /**
     * 开城
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

}
