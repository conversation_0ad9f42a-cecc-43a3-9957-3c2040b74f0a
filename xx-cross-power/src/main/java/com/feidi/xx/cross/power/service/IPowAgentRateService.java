package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowAgentRateVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentRateBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代理商佣金比例Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAgentRateService {

    /**
     * 查询代理商佣金比例
     *
     * @param id 主键
     * @return 代理商佣金比例
     */
    PowAgentRateVo queryById(Long id);

    /**
     * 分页查询代理商佣金比例列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商佣金比例分页列表
     */
    TableDataInfo<PowAgentRateVo> queryPageList(PowAgentRateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代理商佣金比例列表
     *
     * @param bo 查询条件
     * @return 代理商佣金比例列表
     */
    List<PowAgentRateVo> queryList(PowAgentRateBo bo);

    /**
     * 新增代理商佣金比例
     *
     * @param bo 代理商佣金比例
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAgentRateBo bo);

    /**
     * 新增代理商佣金比例
     *
     * @param bos 代理商佣金比例集合
     * @return 是否新增成功
     */
    Boolean insertByBos(List<PowAgentRateBo> bos);

    /**
     * 修改代理商佣金比例
     *
     * @param bo 代理商佣金比例
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAgentRateBo bo);

    /**
     * 校验并批量删除代理商佣金比例信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
