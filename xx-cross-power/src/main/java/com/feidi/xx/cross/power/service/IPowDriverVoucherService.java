package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.core.enums.PtLoginTypeEnum;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.PowDriverVoucher;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverVoucherBo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverVoucherVo;


import java.util.Collection;
import java.util.List;

/**
 * 司机凭证Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface IPowDriverVoucherService {

    /**
     * 查询司机凭证
     *
     * @param id 主键
     * @return 司机凭证
     */
    PowDriverVoucherVo queryById(Long id);

    /**
     * 分页查询司机凭证列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机凭证分页列表
     */
    TableDataInfo<PowDriverVoucherVo> queryPageList(PowDriverVoucherBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机凭证列表
     *
     * @param bo 查询条件
     * @return 司机凭证列表
     */
    List<PowDriverVoucherVo> queryList(PowDriverVoucherBo bo);

    /**
     * 新增司机凭证
     *
     * @param bo 司机凭证
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverVoucherBo bo);

    /**
     * 修改司机凭证
     *
     * @param bo 司机凭证
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverVoucherBo bo);

    /**
     * 校验并批量删除司机凭证信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 判断是否存在
     * @return
     */
    PowDriverVoucher exist(Long driverId, String loginType);

    /**
     *  获取用户的OpenId
     * @param driverId
     * @return
     */
    String getOpenId(Long driverId);
}
