package com.feidi.xx.cross.power.synchronize.service;

import com.feidi.xx.cross.power.synchronize.doamin.*;

import java.util.List;

public interface IPxSyncService {

    /**
     * 查询司机信息
     */
    List<PxDriver> getDriverList();

    /**
     * 根据driverIds
     */
    List<PxDriver> listByDriverIds(List<Long> driverIds);

    /**
     * 查询司机账户信息
     */
    List<PxDriverAccount> getDriverAccountList();

    /**
     * 查询车辆信息
     */
    List<PxCar> getCarList();

    /**
     * 查询证件信息
     */
    List<PxDriverCerts> getDriverCertsList();

    /**
     * 查询代理商信息
     */
    List<PxAgent> getAgentList();
}
