package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverAgreementForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverCarForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverDrivingForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverIdCardForm;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 司机业务对象 pow_driver
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriver.class, reverseConvertGenerate = false)
public class PowDriverBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;


    /**
     * 主键
     */
    private List<Long> ids;

    /**
     * 分组
     */
    private Long groupId;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 上级
     */
    private Long parentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private String sex;

    /**
     * 资产密码
     */
    private String capitalPassword;

    /**
     * 司机类型
     */
    private String type;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 准驾车型
     */
    private String approvedType;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否接单
     */
    private String receive;

    /**
     * 状态
     */
    private String status;

    /**
     * 身份
     */
    private String identity;

    /**
     * 是否合规
     */
    private String legal;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 综合搜索
     */
    private String unionId;

    private String parentAgentName;

    /**
     * 代理商ids
     */
    private List<Long> agentIds;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 司机组名称
     */
    private String groupName;

    /**
     * 常驻城市
     */
    private String residentCity;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 分佣比例
     */
    private BigDecimal rate;

    /**
     * 身份信息
     */
    private PowDriverIdCardForm idCardBo;

    /**
     * 驾驶证信息
     */
    private PowDriverDrivingForm drivingBo;

    /**
     * 车辆信息
     */
    private PowDriverCarForm carBo;

    /**
     * 代扣信息
     */
    private PowDriverAgreementForm agreementBo;

    /**
     * 是否接单 {@link IsYesEnum}
     */
    private String isReceive;

    /**
     * ne身份 {@link DriverIdentityEnum}
     */
    private String neIdentity;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 排除的司机id
     */
    private Long excludedId;

    /**
     * 司机组类型
     */
    private String groupType;

    /**
     * 线路（订单中取）
     */
    private Long lineId;

    /**
     * 驾龄
     */
    private Integer driveYear;

    /**
     * 支付版账号
     */
    private String account;

    /**
     * 支付宝姓名
     */
    private String accountName;

    /**
     * 订单转卖服务费比例
     */
    private BigDecimal resellServiceRate;

    /**
     * 是否分配所有线路 代理商 默认勾选所有
     */
    private Boolean assignRoute = true;


    /**
     * 是否查询待出行订单数
     */
    private Boolean queryPendingTripOrders = false;

    private String lastTime;
}
