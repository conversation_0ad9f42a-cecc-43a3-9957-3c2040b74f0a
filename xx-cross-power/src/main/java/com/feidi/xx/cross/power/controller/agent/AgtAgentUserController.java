package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 代理商账号管理
 * 前端访问路由地址为:/power/agt/agent/user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/agent/user")
public class AgtAgentUserController extends BaseController {

    private final IPowAgentUserService powAgentUserService;

    /**
     * 查询代理商用户列表
     */
    @GetMapping("/list")
    public TableDataInfo<PowAgentUserVo> list(PowAgentUserBo bo, PageQuery pageQuery) {
        return powAgentUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出代理商用户列表
     */
    @Log(title = "代理商用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowAgentUserBo bo, HttpServletResponse response) {
        List<PowAgentUserVo> list = powAgentUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "代理商用户", PowAgentUserVo.class, response);
    }

    /**
     * 获取代理商用户详细信息
     *
     */
    @Log(title = "代理商用户", businessType = BusinessType.EXPORT)
    @GetMapping()
    public R<PowAgentUserVo> getInfo() {
        Long userId = LoginHelper.getUserId();
        return R.ok(powAgentUserService.queryById(userId));
    }

    /**
     * 新增代理商用户
     */
    @Log(title = "代理商用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentUserBo bo) {
        return toAjax(powAgentUserService.insertByBo(bo));
    }

    /**
     * 修改代理商用户
     */
    @Log(title = "代理商用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAgentUserBo bo) {
        if (!LoginHelper.getUserId().equals(bo.getId())) {
            return R.fail("不允许操作其他代理商用户");
        }
        return toAjax(powAgentUserService.updateByBo(bo));
    }

    /**
     * 删除代理商用户
     *
     * @param ids 主键串
     */
    @Log(title = "代理商用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(powAgentUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
