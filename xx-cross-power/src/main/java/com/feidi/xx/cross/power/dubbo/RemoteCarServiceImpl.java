package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteCarService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.mapper.PowCarMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 车辆服务接口dubbo实现
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteCarServiceImpl implements RemoteCarService {

    private final PowCarMapper powCarMapper;

    /**
     * 根据车辆ID查询车辆信息
     *
     * @param carId 车辆id
     * @return 车辆信息
     */
    @Override
    public RemoteCarVo getCarInfoById(Long carId) {
        PowCar powCar = powCarMapper.selectById(carId);

        return BeanUtils.copyProperties(powCar, RemoteCarVo.class);
    }

    /**
     * 根据司机ID查询车辆信息
     *
     * @param driverId 司机id
     * @return 车辆信息
     */
    @Override
    public RemoteCarVo getCarInfoByDriverId(Long driverId) {
        LambdaQueryWrapper<PowCar> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowCar::getDriverId, driverId)
                .orderByDesc(PowCar::getCreateTime)
                .last(Constants.LIMIT_ONE);

        PowCar powCar = powCarMapper.selectOne(lqw);

        return BeanUtils.copyProperties(powCar, RemoteCarVo.class);
    }

    /**
     * 根据司机ID集合查询车辆信息
     *
     * @param driverIds
     * @return 车辆信息
     */
    @Override
    public List<RemoteCarVo> queryByDriverIds(List<Long> driverIds) {
        if (CollUtils.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PowCar> lqw = Wrappers.lambdaQuery();
        lqw.in(PowCar::getDriverId, driverIds);

        List<PowCar> powCars = powCarMapper.selectList(lqw);

        return BeanUtils.copyToList(powCars, RemoteCarVo.class);
    }
}
