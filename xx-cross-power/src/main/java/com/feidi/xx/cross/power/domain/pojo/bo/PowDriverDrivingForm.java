package com.feidi.xx.cross.power.domain.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.validate.ReviewGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 司机申请驾驶证信息业务对象
 */
@Data
@AutoMapper(target = PowDriver.class, reverseConvertGenerate = false)
public class PowDriverDrivingForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 驾驶证所属人
     */
    @NotBlank(message = "姓名不能为空", groups = {ReviewGroup.class})
    private String certsOwner;

    /**
     * 准驾车型[ApprovedTypeEnum]
     */
    @NotBlank(message = "准驾车型不能为空", groups = {ReviewGroup.class})
    private String approvedType;

    /**
     * 初次领证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "初次领证日期不能为空", groups = {ReviewGroup.class})
    private Date firstTime;

    /**
     * 有效期截至日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "有效截止日期不能为空", groups = {ReviewGroup.class})
    private Date endTime;

    /**
     * 驾驶证(正页)
     */
    @NotNull(message = "驾驶证(正页)不能为空", groups = {ReviewGroup.class})
    private Long frontOssId;

    /**
     * 驾驶证(副页)
     */
    @NotNull(message = "驾驶证(副页)不能为空", groups = {ReviewGroup.class})
    private Long backOssId;

    /**
     * 驳回原因
     */
    private List<String> reasons;
}
