package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 司机组业务对象 pow_group
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowGroup.class, reverseConvertGenerate = false)
public class PowGroupBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商主体名称
     */
    private String mainBody;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 图标
     */
    private String icon;

    /**
     * 类型[DriverGroupTypeEnum]
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否同步[IsYesEnum]
     */
    private String sync;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


}
