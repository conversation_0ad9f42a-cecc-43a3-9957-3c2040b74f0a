package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverVoucherBo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverVoucherVo;
import com.feidi.xx.cross.power.service.IPowDriverVoucherService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机凭证
 * 前端访问路由地址为:/power/driverVoucher
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driverVoucher")
public class PowDriverVoucherController extends BaseController {

    private final IPowDriverVoucherService ptDriverVoucherService;

    /**
     * 查询司机凭证列表
     */
    @SaCheckPermission("power:powerDriverVoucher:list")
    @GetMapping("/list")
    public TableDataInfo<PowDriverVoucherVo> list(PowDriverVoucherBo bo, PageQuery pageQuery) {
        return ptDriverVoucherService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机凭证列表
     */
    @SaCheckPermission("power:powerDriverVoucher:export")
    @Log(title = "司机凭证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody PowDriverVoucherBo bo, HttpServletResponse response) {
        List<PowDriverVoucherVo> list = ptDriverVoucherService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机凭证", PowDriverVoucherVo.class, response);
    }

    /**
     * 获取司机凭证详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerDriverVoucher:query")
    @GetMapping("/{id}")
    public R<PowDriverVoucherVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ptDriverVoucherService.queryById(id));
    }

    /**
     * 新增司机凭证
     */
    @SaCheckPermission("power:powerDriverVoucher:add")
    @Log(title = "司机凭证", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverVoucherBo bo) {
        return toAjax(ptDriverVoucherService.insertByBo(bo));
    }

    /**
     * 修改司机凭证
     */
    @SaCheckPermission("power:powerDriverVoucher:edit")
    @Log(title = "司机凭证", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverVoucherBo bo) {
        return toAjax(ptDriverVoucherService.updateByBo(bo));
    }

    /**
     * 删除司机凭证
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerDriverVoucher:remove")
    @Log(title = "司机凭证", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ptDriverVoucherService.deleteWithValidByIds(List.of(ids), true));
    }
}
