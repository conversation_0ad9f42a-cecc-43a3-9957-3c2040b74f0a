package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.service.IPowDriverService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 司机审核
 * 前端访问路由地址为:/power/agt/driver/review
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/driver/review")
public class AgtDriverReviewController extends BaseController {

    private final IPowDriverService powDriverService;

    /**
     * 查询司机列表 - 审核列表
     */
    @GetMapping("/list")
    public TableDataInfo<PowDriverVo> list(PowDriverBo bo, PageQuery pageQuery) {
        return powDriverService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机列表
     */
    @Log(title = "司机审核", businessType = BusinessType.EXPORT)
    @Download(name="司机审核",module = ModuleConstants.POWER)
    @PostMapping("/export")
    public void export(PowDriverBo bo, HttpServletResponse response) {
        List<PowDriverVo> list = powDriverService.queryList(bo);
        ExcelUtil.exportExcel(list, "司机审核", PowDriverVo.class, response);
    }

    /**
     * 获取司机详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowDriverVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powDriverService.queryById(id));
    }

    /**
     * 司机 - 审核
     */
    @Log(title = "司机审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> review(@Validated(EditGroup.class) @RequestBody PowDriverBo bo) {
        return toAjax(powDriverService.updateByBo(bo));
    }
}
