package com.feidi.xx.cross.power.domain.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 加盟驾驶证视图对象
 */
@Data
@Accessors(chain = true)
public class PowDrvDrivingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 脱敏驾驶证姓名
     */
    private String desensitizedName;

    /**
     * 脱敏准驾车型
     */
    private String desensitizedApprovedType;

    /**
     * 驾驶证所属人
     */
    private String certsOwner;

    /**
     * 准驾车型
     */
    private String approvedType;

    /**
     * 初次领证日期
     */
    private Date firstTime;

    /**
     * 有效期截至日期
     */
    private Date endTime;

    /**
     * 驾驶证(正页)
     */
    private Long frontOssId;

    /**
     * 驾驶证(副页)
     */
    private Long backOssId;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 驾驶证正页url
     */
    private String frontImg;

    /**
     * 驾驶证副页url
     */
    private String backImg;
}
