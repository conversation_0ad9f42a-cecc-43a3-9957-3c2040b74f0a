package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentLineBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentLineVo;
import com.feidi.xx.cross.power.service.IPowAgentLineService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商 - 代理商线路
 * 前端访问路由地址为:/power/agt/agent/line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/agent/line")
public class AgtAgentLineController extends BaseController {

    private final IPowAgentLineService powAgentLineService;

    /**
     * 获取代理商线路详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowAgentLineVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(powAgentLineService.queryById(id));
    }

    /**
     * 新增代理商线路
     */
    @Log(title = "代理商线路", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentLineBo bo) {
        return toAjax(powAgentLineService.insertByBo(bo));
    }



}
