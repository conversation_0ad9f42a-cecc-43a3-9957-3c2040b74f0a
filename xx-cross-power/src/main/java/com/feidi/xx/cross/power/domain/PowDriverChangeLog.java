package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 司机信息变更记录对象 pow_driver_change_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_driver_change_log")
public class PowDriverChangeLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 老数据
     */
    private String oldValue;

    /**
     * 新数据
     */
    private String newValue;

    /**
     * 扩展
     */
    private String extra;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作名称
     */
    private String title;

    /**
     * 展示信息
     */
    private String msg;


}
