package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.power.domain.PowDriverCerts;
import com.feidi.xx.cross.power.domain.vo.PowDriverCertsVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 司机证件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowDriverCertsMapper extends BaseMapperPlus<PowDriverCerts, PowDriverCertsVo> {
    default List<PowDriverCerts> listByDriverId(Long driverId){
        return selectList(Wrappers.<PowDriverCerts>lambdaQuery().eq(PowDriverCerts::getDriverId, driverId));
    }

}
