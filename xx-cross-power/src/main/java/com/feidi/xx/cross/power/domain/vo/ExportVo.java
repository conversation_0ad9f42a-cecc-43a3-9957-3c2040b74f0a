package com.feidi.xx.cross.power.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 导出对象
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String url;
}
