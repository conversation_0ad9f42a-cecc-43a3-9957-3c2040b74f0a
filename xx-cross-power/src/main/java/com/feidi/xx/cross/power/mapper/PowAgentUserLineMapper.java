package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.power.domain.PowAgentUserLine;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserLineVo;

import java.util.Collection;
import java.util.List;

/**
 * 代理商用户线路Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface PowAgentUserLineMapper extends BaseMapperPlus<PowAgentUserLine, PowAgentUserLineVo> {

    default List<PowAgentUserLine> listByAgentUserId(Long agentUserId) {
        LambdaQueryWrapper<PowAgentUserLine> lqw = new LambdaQueryWrapper<PowAgentUserLine>()
                .eq(PowAgentUserLine::getAgentUserId, agentUserId);
        return selectList(lqw);
    }

    default List<PowAgentUserLine> listByAgentUserIds(Collection<Long> agentUserIds) {
        LambdaQueryWrapper<PowAgentUserLine> lqw = new LambdaQueryWrapper<PowAgentUserLine>()
                .in(PowAgentUserLine::getAgentUserId, agentUserIds);
        return selectList(lqw);
    }

    default List<PowAgentUserLine> listByLineId(Long lineId) {
        LambdaQueryWrapper<PowAgentUserLine> lqw = new LambdaQueryWrapper<PowAgentUserLine>()
                .eq(PowAgentUserLine::getLineId, lineId);
        return selectList(lqw);
    }

    default List<PowAgentUserLine> listByLineIds(Collection<Long> lineIds) {
        LambdaQueryWrapper<PowAgentUserLine> lqw = new LambdaQueryWrapper<PowAgentUserLine>()
                .in(PowAgentUserLine::getLineId, lineIds);
        return selectList(lqw);
    }

    default int deleteByAgentUserId(Long agentUserId) {
        return delete(Wrappers.<PowAgentUserLine>lambdaQuery()
                .eq(PowAgentUserLine::getAgentUserId, agentUserId)
        );
    }

    default int deleteByLineId(Long lineId) {
        return delete(Wrappers.<PowAgentUserLine>lambdaQuery()
                .eq(PowAgentUserLine::getLineId, lineId));
    }

    default boolean deleteByLineIds(Long agentUserId, Collection<Long> lineIds) {
        return delete(new LambdaQueryWrapper<PowAgentUserLine>()
                .eq(PowAgentUserLine::getAgentUserId, agentUserId)
                .in(PowAgentUserLine::getLineId, lineIds)) > 0;
    }


    default int deleteByAgentUserIds(Long lineId, Collection<Long> agentUserIds) {
        return delete(Wrappers.<PowAgentUserLine>lambdaQuery()
                .eq(PowAgentUserLine::getLineId, lineId)
                .in(PowAgentUserLine::getAgentUserId, agentUserIds));
    }
}
