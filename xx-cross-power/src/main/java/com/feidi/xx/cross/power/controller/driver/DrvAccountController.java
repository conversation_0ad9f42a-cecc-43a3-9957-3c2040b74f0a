package com.feidi.xx.cross.power.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;

import com.feidi.xx.cross.power.domain.bo.PowDriverAccountBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.service.IPowDriverAccountService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机端 - 提现钱包管理
 * 前端访问路由地址为:/power/drv
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/account")
public class DrvAccountController extends BaseController {

    private final IPowDriverAccountService powDriverAccountService;

    /**
     * 查询司机账户列表
     */
    @GetMapping("/list")
    public R<List<PowDriverAccountVo>> list() {
        PowDriverAccountBo bo = new PowDriverAccountBo();
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(powDriverAccountService.queryList(bo));
    }

    /**
     * 获取司机账户详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PowDriverAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(powDriverAccountService.queryById(id));
    }

    /**
     * 新增司机账户
     */
    @Log(title = "司机账户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowDriverAccountBo bo) {
        return toAjax(powDriverAccountService.insertByBo(bo));
    }

    /**
     * 修改司机账户
     */
    @Log(title = "司机账户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowDriverAccountBo bo) {
        return toAjax(powDriverAccountService.updateByBo(bo));
    }

    /**
     * 删除司机账户
     *
     * @param ids 主键串
     */
    @Log(title = "司机账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powDriverAccountService.deleteWithValidByIds(List.of(ids), true));
    }

}
