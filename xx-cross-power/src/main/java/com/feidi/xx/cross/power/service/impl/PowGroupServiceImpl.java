package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.cache.power.vo.PowGroupCacheVo;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.power.DriverGroupTypeEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.PowDriverRate;
import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.cross.power.domain.bo.PowGroupBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.mapper.PowDriverMapper;
import com.feidi.xx.cross.power.mapper.PowDriverRateMapper;
import com.feidi.xx.cross.power.mapper.PowGroupMapper;
import com.feidi.xx.cross.power.service.IPowGroupService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 司机组Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@RequiredArgsConstructor
@Service
public class PowGroupServiceImpl implements IPowGroupService {

    private static final Logger log = LoggerFactory.getLogger(PowGroupServiceImpl.class);
    private final PowGroupMapper baseMapper;
    private final PowDriverMapper driverMapper;
    private final PowAgentMapper agentMapper;
    private final PowDriverRateMapper powDriverRateMapper;
    private final ScheduledExecutorService scheduledExecutorService;

    /**
     * 查询司机组
     *
     * @param id 主键
     * @return 司机组
     */
    @Override
    public PowGroupVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机组分页列表
     */
    @Override
    public TableDataInfo<PowGroupVo> queryPageList(PowGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowGroup> lqw = buildQueryWrapper(bo);
        Page<PowGroupVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<PowGroupVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Long[] groupIds = result.getRecords().stream().map(PowGroupVo::getId).toArray(Long[]::new);
            List<PowDriver> drivers = driverMapper.listByGroupId(groupIds);
            // 只统计审核通过的司机
            Map<Long, Long> map = drivers.stream()
                    .filter(d -> Objects.equals(d.getAuditStatus(), DrvAuditStatusEnum.SUCCESS.getCode()))
                    .collect(Collectors.groupingBy(PowDriver::getGroupId, Collectors.counting()));
            records.parallelStream().forEach(e -> e.setNum(map.getOrDefault(e.getId(), 0L)));
        }
        return new TableDataInfo<>(records, result.getTotal());
    }

    /**
     * 查询符合条件的司机组列表
     *
     * @param bo 查询条件
     * @return 司机组列表
     */
    @Override
    public <T> List<T> queryList(PowGroupBo bo, Class<T> clazz) {
        LambdaQueryWrapper<PowGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, clazz);
    }

    private LambdaQueryWrapper<PowGroup> buildQueryWrapper(PowGroupBo bo) {
        LambdaQueryWrapper<PowGroup> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), PowGroup::getName, bo.getName());
        lqw.eq(bo.getAgentId() != null, PowGroup::getAgentId, bo.getAgentId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), PowGroup::getType, bo.getType());
        lqw.ge(ObjectUtils.isNotNull(bo.getStartTime()), PowGroup::getCreateTime, bo.getStartTime());
        lqw.le(ObjectUtils.isNotNull(bo.getEndTime()), PowGroup::getCreateTime, bo.getEndTime());
        lqw.orderByDesc(PowGroup::getCreateTime);
        return lqw;
    }

    /**
     * 新增司机组
     *
     * @param bo 司机组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowGroupBo bo) {
        PowGroup add = MapstructUtils.convert(bo, PowGroup.class);
        validEntityBeforeSave(add);
        // 生成司机组编号
        add.setCode(generateGroupCode(add.getType()));
        // 添加缓存
        scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改司机组
     *
     * @param bo 司机组
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PowGroupBo bo) {
        PowGroup update = MapstructUtils.convert(bo, PowGroup.class);
        validEntityBeforeSave(update);
        if (bo.getAgentId() != null) {
            PowAgentVo agentVo = agentMapper.selectVoById(bo.getAgentId());
            update.setMainBody(agentVo.getCompanyName());
        }

        // 同步修改司机比例
        if (Objects.equals(bo.getSync(), IsYesEnum.YES.getCode())) {
            LambdaQueryWrapper<PowDriver> lqw = Wrappers.lambdaQuery();
            lqw.eq(PowDriver::getGroupId, bo.getId());
            List<Long> driverIds = driverMapper.selectList(lqw)
                    .stream().map(PowDriver::getId).collect(Collectors.toList());

            if (CollUtils.isNotEmpty(driverIds)) {
                log.info("同步修改司机比例-司机组id: 【{}】，司机id: 【{}】", bo.getId(), JsonUtils.toJsonString(driverIds));
                LambdaUpdateWrapper<PowDriverRate> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(PowDriverRate::getRate, bo.getRate())
                        .set(PowDriverRate::getUpdateTime, DateUtils.getNowDate())
                        .in(PowDriverRate::getDriverId, driverIds);
                powDriverRateMapper.update(updateWrapper);

                // 异步删除司机利率缓存
                scheduledExecutorService.schedule(() -> {
                    for (Long driverId : driverIds) {
                        String cacheKey = PowCacheKeyEnum.POW_DRIVER_RATE_CACHE_KEY.create(driverId, PlatformCodeEnum.TY.getCode());
                        RedisUtils.deleteObject(cacheKey);
                    }
                }, 0, TimeUnit.SECONDS);

            }
        }

        // 清除缓存
        scheduledExecutorService.schedule(() -> removeCache(bo.getId()), 0, TimeUnit.SECONDS);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowGroup entity){
        // 同公司下司机组名称不允许重复
        LambdaQueryWrapper<PowGroup> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowGroup::getAgentId, entity.getAgentId())
                .eq(PowGroup::getName, entity.getName())
                .ne(entity.getId() != null, PowGroup::getId, entity.getId());
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("同公司下司机组名称不允许重复");
        }

        if (StringUtils.isBlank(entity.getMainBody())) {
            PowAgentVo agentVo = agentMapper.selectVoById(entity.getAgentId());
            entity.setMainBody(agentVo.getCompanyName());
        }
    }

    /**
     * 校验并批量删除司机组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Set<Long> removeIds = new HashSet<>();
        boolean error = false;
        for (Long id : ids) {
            // 司机组下面有司机不能删除
            if (CollUtil.isEmpty(driverMapper.listByGroupId(id))) {
                removeIds.add(id);
            } else {
                error = true;
            }
        }
        if (CollUtil.isNotEmpty(removeIds)) {
            baseMapper.deleteByIds(removeIds);

            scheduledExecutorService.schedule(() -> ids.forEach(this::removeCache), 0, TimeUnit.SECONDS);
        }
        if (error) {
            throw new ServiceException("部分司机组下存在司机，无法删除");
        }
        return true;
    }

    /**
     * 根据主键查询司机组信息，并缓存
     *
     * @param ids 主键集合
     * @return 司机组信息
     */
    @Override
    public List<PowGroup> queryByIdsAndCacheInfo(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<PowGroup> pxGroups = baseMapper.selectBatchIds(ids);

        // 添加缓存
        scheduledExecutorService.schedule(() -> pxGroups.forEach(this::addCache), 0, TimeUnit.SECONDS);
        return pxGroups;
    }

    /**
     * 根据主键查询司机组信息
     *
     * @param groupId 司机组主键
     * @return 司机组信息
     */
    @Override
    public PowGroup getGroupInfo(Long groupId) {
        List<PowGroup> groupInfo = this.getGroupInfo(Collections.singletonList(groupId));
        return CollUtil.isNotEmpty(groupInfo) ? groupInfo.get(0) : null;
    }

    /**
     * 根据主键查询司机组信息
     *
     * @param groupIds 主键集合
     * @return 司机组信息
     */
    @Override
    public List<PowGroup> getGroupInfo(List<Long> groupIds) {
        List<PowGroup> pxGroups = new ArrayList<>();
        List<Long> notExistCacheIds = new ArrayList<>();
        groupIds.forEach(groupId -> {
            String key = PowCacheKeyEnum.POW_GROUP_INFO_CACHE_KEY.create(groupId);
            if (RedisUtils.hasKey(key)) {
                pxGroups.add(BeanUtils.copyProperties(RedisUtils.getCacheObject(key), PowGroup.class));
            } else {
                notExistCacheIds.add(groupId);
            }
        });

        if (CollUtil.isNotEmpty(notExistCacheIds)) {
            pxGroups.addAll(this.queryByIdsAndCacheInfo(notExistCacheIds));
        }

        return pxGroups;
    }

    /**
     * 添加司机组缓存
     *
     * @param group 司机组
     */
    public void addCache(PowGroup group) {
        if (group != null) {
            String cacheKay = PowCacheKeyEnum.POW_GROUP_INFO_CACHE_KEY.create(group.getId());
            RedisUtils.setCacheObject(cacheKay, BeanUtils.copyProperties(group, PowGroupCacheVo.class), PowCacheKeyEnum.POW_GROUP_INFO_CACHE_KEY.getDuration());
        }
    }

    /**
     * 删除司机组缓存
     *
     * @param groupId 司机组id
     */
    public void removeCache(Long groupId) {
        RedisUtils.deleteObject(PowCacheKeyEnum.POW_GROUP_INFO_CACHE_KEY.create(groupId));
    }

    /**
     * 生成司机组编号
     * 生成规则：”自营”1开头的8位随机数字，”加盟”2开头的8位随机数据
     *
     * @param groupType 司机组类型
     * @return 司机组编号
     */
    private String generateGroupCode(String groupType) {
        if (Objects.equals(groupType, DriverGroupTypeEnum.SELF.getCode())) {
            return "1" + generateGroupCode();
        } else if (Objects.equals(groupType, DriverGroupTypeEnum.PARTNER.getCode())) {
            return "2" + generateGroupCode();
        } else {
            throw new ServiceException("司机组类型错误");
        }
    }

    /**
     * 生成司机组编号
     * 生成规则：HHmmss + 一位随机数
     *
     * @return 司机组编号
     */
    private String generateGroupCode() {
        return DateUtil.format(DateUtils.getNowDate(), DatePattern.PURE_TIME_PATTERN) + RandomUtil.randomInt(1, 9);
    }
}
