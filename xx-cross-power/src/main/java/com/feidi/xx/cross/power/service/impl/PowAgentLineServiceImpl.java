package com.feidi.xx.cross.power.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.power.domain.PowAgentCity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowAgentLineBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentLineVo;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.cross.power.mapper.PowAgentLineMapper;
import com.feidi.xx.cross.power.service.IPowAgentLineService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 代理商线路Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAgentLineServiceImpl implements IPowAgentLineService {

    private final PowAgentLineMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;

    /**
     * 查询代理商线路
     *
     * @param id 主键
     * @return 代理商线路
     */
    @Override
    public PowAgentLineVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询代理商线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商线路分页列表
     */
    @Override
    public TableDataInfo<PowAgentLineVo> queryPageList(PowAgentLineBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAgentLine> lqw = buildQueryWrapper(bo);
        Page<PowAgentLineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代理商线路列表
     *
     * @param bo 查询条件
     * @return 代理商线路列表
     */
    @Override
    public List<PowAgentLineVo> queryList(PowAgentLineBo bo) {
        LambdaQueryWrapper<PowAgentLine> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PowAgentLine> buildQueryWrapper(PowAgentLineBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowAgentLine> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getLineId() != null, PowAgentLine::getLineId, bo.getLineId());
        lqw.eq(bo.getAgentId() != null, PowAgentLine::getAgentId, bo.getAgentId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PowAgentLine::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增代理商线路
     *
     * @param bo 代理商线路
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowAgentLineBo bo) {
        PowAgentLine add = MapstructUtils.convert(bo, PowAgentLine.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改代理商线路
     *
     * @param bo 代理商线路
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowAgentLineBo bo) {
        PowAgentLine update = MapstructUtils.convert(bo, PowAgentLine.class);
        validEntityBeforeSave(update);
        boolean ret = baseMapper.updateById(update) > 0;
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);
        return ret;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAgentLine entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代理商线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<Long> listByAgentId(Long agentId) {
        List<PowAgentLine> agentLines = baseMapper.listByAgentId(agentId);
        return agentLines.stream().map(PowAgentLine::getLineId).toList();
    }
    /**
     * 添加缓存
     *
     * @param agentLine 产品信息
     */
    private void addCache(PowAgentLine agentLine) {
        if (agentLine == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.create(agentLine.getAgentId());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(agentLine, RemoteCityVo.class),
                PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.getDuration());
    }

    /**
     * 删除缓存
     *
     * @param agentId
     */
    private void deleteCache(Long agentId) {
        RedisUtils.deleteObject(PowCacheKeyEnum.POW_AGENT_CITY_CACHE_KEY.create(agentId));
    }
}
