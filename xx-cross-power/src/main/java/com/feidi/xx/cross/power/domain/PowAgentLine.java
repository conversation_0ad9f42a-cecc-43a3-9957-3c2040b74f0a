package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 代理商线路对象 pow_agent_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_agent_line")
public class PowAgentLine extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 路线
     */
    private Long lineId;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 父代理商ID
     */
    private Long parentId;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    public PowAgentLine(Long agentId, Long parentId, Long lineId) {
        this.agentId = agentId;
        this.parentId = parentId;
        this.lineId = lineId;
    }
}
