package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 代理商线路业务对象 pow_agent_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAgentLine.class, reverseConvertGenerate = false)
public class PowAgentLineBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 路线
     */
    @NotNull(message = "路线不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 代理商
     */
    @NotNull(message = "代理商不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 状态[StatusEnum]
     */
    @NotBlank(message = "状态[StatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
