package com.feidi.xx.cross.power.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowDriverCertsBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverCertsVo;
import com.feidi.xx.cross.power.domain.PowDriverCerts;
import com.feidi.xx.cross.power.mapper.PowDriverCertsMapper;
import com.feidi.xx.cross.power.service.IPowDriverCertsService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 司机证件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowDriverCertsServiceImpl implements IPowDriverCertsService {

    private final PowDriverCertsMapper baseMapper;

    /**
     * 查询司机证件
     *
     * @param id 主键
     * @return 司机证件
     */
    @Override
    public PowDriverCertsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机证件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机证件分页列表
     */
    @Override
    public TableDataInfo<PowDriverCertsVo> queryPageList(PowDriverCertsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriverCerts> lqw = buildQueryWrapper(bo);
        Page<PowDriverCertsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机证件列表
     *
     * @param bo 查询条件
     * @return 司机证件列表
     */
    @Override
    public List<PowDriverCertsVo> queryList(PowDriverCertsBo bo) {
        LambdaQueryWrapper<PowDriverCerts> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<PowDriverCertsVo> queryListByDriver(Long driverId) {
        PowDriverCertsBo bo = new PowDriverCertsBo();
        bo.setDriverId(driverId);
        return queryList(bo);
    }

    private LambdaQueryWrapper<PowDriverCerts> buildQueryWrapper(PowDriverCertsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowDriverCerts> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDriverId() != null, PowDriverCerts::getDriverId, bo.getDriverId());
        lqw.eq(StringUtils.isNotBlank(bo.getCertsOwner()), PowDriverCerts::getCertsOwner, bo.getCertsOwner());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), PowDriverCerts::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PowDriverCerts::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增司机证件
     *
     * @param bo 司机证件
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowDriverCertsBo bo) {
        PowDriverCerts add = MapstructUtils.convert(bo, PowDriverCerts.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机证件
     *
     * @param bo 司机证件
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowDriverCertsBo bo) {
        PowDriverCerts update = MapstructUtils.convert(bo, PowDriverCerts.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriverCerts entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除司机证件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
