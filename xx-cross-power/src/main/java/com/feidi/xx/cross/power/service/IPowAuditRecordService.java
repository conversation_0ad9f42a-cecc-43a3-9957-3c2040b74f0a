package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowAuditRecordVo;
import com.feidi.xx.cross.power.domain.bo.PowAuditRecordBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 审核记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAuditRecordService {

    /**
     * 查询审核记录
     *
     * @param id 主键
     * @return 审核记录
     */
    PowAuditRecordVo queryById(Long id);

    /**
     * 分页查询审核记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 审核记录分页列表
     */
    TableDataInfo<PowAuditRecordVo> queryPageList(PowAuditRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的审核记录列表
     *
     * @param bo 查询条件
     * @return 审核记录列表
     */
    List<PowAuditRecordVo> queryList(PowAuditRecordBo bo);

    /**
     * 新增审核记录
     *
     * @param bo 审核记录
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAuditRecordBo bo);

    /**
     * 修改审核记录
     *
     * @param bo 审核记录
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAuditRecordBo bo);

    /**
     * 校验并批量删除审核记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
