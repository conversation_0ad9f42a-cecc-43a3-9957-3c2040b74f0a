package com.feidi.xx.cross.power.synchronize.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.power.synchronize.doamin.PxAgent;
import com.feidi.xx.cross.power.synchronize.doamin.vo.PxAgentVo;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 代理商Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface PxAgentMapper extends BaseMapperPlus<PxAgent, PxAgentVo> {

    default PxAgent queryByPhone(String phone){
        return selectOne(new LambdaQueryWrapper<PxAgent>().eq(PxAgent::getPhone, phone));
    }

    /**
     * 根据邀请码
     */
    default PxAgent queryByCode(String code){
        return selectOne(new LambdaQueryWrapper<PxAgent>().eq(PxAgent::getCode, code));
    }

    default List<PxAgent> listByParentId(Long agentId){
        return selectList(new LambdaQueryWrapper<PxAgent>().eq(PxAgent::getParentId, agentId));
    }

    default List<PxAgent> listByParentIds(Collection<Long> agentIds){
        return selectList(new LambdaQueryWrapper<PxAgent>().in(PxAgent::getParentId, agentIds));
    }

    default boolean existsByCompany(String mainBody, String creditCode){
        return exists(Wrappers.<PxAgent>lambdaQuery()
                .nested(e -> e.eq(PxAgent::getMainBody, mainBody)
                        .eq(PxAgent::getCreditCode, creditCode))
                );
    }

    default void disableAgent(Long... agentIds) {
        update(Wrappers.<PxAgent>lambdaUpdate()
                .in(PxAgent::getId, Arrays.asList(agentIds))
                .set(PxAgent::getStatus, StatusEnum.DISABLE.getCode())
                .eq(PxAgent::getStatus, StatusEnum.ENABLE.getCode())
        );
    }

    default void enableAgent(Long... agentIds) {
        update(Wrappers.<PxAgent>lambdaUpdate()
                .in(PxAgent::getId, Arrays.asList(agentIds))
                .set(PxAgent::getStatus, StatusEnum.ENABLE.getCode())
                .eq(PxAgent::getStatus, StatusEnum.DISABLE.getCode())
        );
    }

}
