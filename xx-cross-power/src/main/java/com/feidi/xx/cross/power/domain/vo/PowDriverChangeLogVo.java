package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.power.domain.PowDriverChangeLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 司机信息变更记录视图对象 pow_driver_change_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriverChangeLog.class)
public class PowDriverChangeLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机ID", index = 2)
    private Long driverId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "所属代理商", index = 1)
    private String agentName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "司机姓名", index = 4)
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号码", index = 3)
    private String phone;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 老数据
     */
    private String oldValue;

    /**
     * 新数据
     */
    private String newValue;

    /**
     * 扩展
     */
    private String extra;

    /**
     * 备注
     */
    @ExcelProperty(value = "操作原因", index = 6)
    private String remark;

    /**
     * 操作名称
     */
    private String title;

    /**
     * 展示信息
     */
    @ExcelProperty(value = "说明", index = 5)
    private String msg;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @ExcelProperty(value = "操作人", index = 0)
    private String createName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "操作时间", index = 7)
    private Date createTime;

}
