package com.feidi.xx.cross.power.synchronize.doamin;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 司机证件对象 px_driver_certs
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("px_driver_certs")
public class PxDriverCerts extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 上级id
     */
    private Long driverId;

    /**
     * 证件类型[PxDrvCertTypeEnum]
     */
    private String type;

    /**
     * 证件id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Long ossId;

    /**
     * 第一次领证时间
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Date firstTime;

    /**
     * 开始时间
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Date endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}
