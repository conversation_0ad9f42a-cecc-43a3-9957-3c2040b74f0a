package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBatchBo;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentCityVo;

import java.util.Collection;
import java.util.List;

/**
 * 代理商城市Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowAgentCityService {

    /**
     * 查询代理商城市
     *
     * @param id 主键
     * @return 代理商城市
     */
    PowAgentCityVo queryById(Long id);

    /**
     * 分页查询代理商城市列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商城市分页列表
     */
    TableDataInfo<PowAgentCityVo> queryPageList(PowAgentCityBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代理商城市列表
     *
     * @param bo 查询条件
     * @return 代理商城市列表
     */
    List<PowAgentCityVo> queryList(PowAgentCityBo bo);

    /**
     * 新增代理商城市
     *
     * @param bo 代理商城市
     * @return 是否新增成功
     */
    Boolean insertByBo(PowAgentCityBo bo);

    /**
     * 修改代理商城市
     *
     * @param bo 代理商城市
     * @return 是否修改成功
     */
    Boolean updateByBo(PowAgentCityBo bo);

    /**
     * 校验并批量删除代理商城市信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertByBoToList(PowAgentCityBatchBo batchBo);
}
