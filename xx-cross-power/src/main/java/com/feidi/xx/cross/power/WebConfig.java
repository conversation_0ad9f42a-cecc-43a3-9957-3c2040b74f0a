package com.feidi.xx.cross.power;

import cn.hutool.core.date.DatePattern;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.DateFormatter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全局日期格式化配置
 * 统一日期格式为yyyy-MM-dd HH:mm:ss
 * GET 请求参数日期格式化
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addFormatterForFieldType(java.util.Date.class, new DateFormatter(DatePattern.NORM_DATETIME_PATTERN));
    }
}