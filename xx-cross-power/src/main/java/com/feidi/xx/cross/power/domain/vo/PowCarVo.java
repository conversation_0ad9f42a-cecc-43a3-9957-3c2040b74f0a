package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.cross.common.enums.power.CarStyleEnum;
import com.feidi.xx.cross.common.enums.power.DriveTypeEnum;
import com.feidi.xx.cross.power.domain.PowCar;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 车辆视图对象 pow_car
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowCar.class)
public class PowCarVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机ID
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;
    private String driverName;

    @ExcelProperty(value = "司机手机号")
    private String driverPhone;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String carNumber;

    /**
     * 车辆品牌
     */
    @ExcelProperty(value = "车辆品牌")
    private String carBrand;

    /**
     * 车辆型号
     */
    @ExcelProperty(value = "车辆型号")
    private String carModel;

    /**
     * 车辆颜色
     */
    @ExcelProperty(value = "车辆颜色")
    private String carColor;

    /**
     * 车辆样式 PowCarStyleEnum
     */
    private String carStyle;
    @ExcelProperty(value = "车辆样式")
    private String carStyleText;
    public String getCarStyleText () {
        return CarStyleEnum.getInfoByCode(carStyle);
    }

    /**
     * 驱动方式 PowDriveTypeEnum
     */
    private String driveType;
    @ExcelProperty(value = "驱动方式")
    private String driveTypeText;
    public String getDriveTypeText () {
        return DriveTypeEnum.getInfoByCode(driveType);
    }

    /**
     * 车辆级别
     */
    @ExcelProperty(value = "车辆级别")
    private String level;


    /**
     * 车辆识别码（车架号）
     */
    @ExcelProperty(value = "车辆识别码")
    private String vin;

    /**
     * 发动机编号
     */
    @ExcelProperty(value = "发动机编号")
    private String engine;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private String latitude;

    /**
     * 座位数
     */
    @ExcelProperty(value = "座位数")
    private Integer seat;

    /**
     * 车辆图片
     */
    private String carPic;

    /**
     * 状态
     */
    private String status;
    @ExcelProperty(value = "状态")
    private String statusText;
    public String getStatusText () {
        return StatusEnum.getInfoByCode(status);
    }

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;

}
