package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBatchBo;
import com.feidi.xx.cross.power.domain.bo.PowAgentCityBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentCityVo;
import com.feidi.xx.cross.power.service.IPowAgentCityService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 代理商城市
 * 前端访问路由地址为:/power/agent/city
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/agent/city")
public class PowAgentCityController extends BaseController {

    private final IPowAgentCityService powAgentCityService;


    /**
     * 获取代理商城市详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerAgentCity:query")
    @GetMapping("/{id}")
    public R<PowAgentCityVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powAgentCityService.queryById(id));
    }



    /**
     * 查询代理商城市列表
     */
    @SaCheckPermission("power:powerAgentCity:list")
    @GetMapping("/list")
    public R<TableDataInfo<PowAgentCityVo>> list(PowAgentCityBo bo, PageQuery pageQuery) {
        return R.ok(powAgentCityService.queryPageList(bo, pageQuery));
    }
    /**
     * 查询代理商城市列表-全部
     */
    @SaCheckPermission("power:powerAgentCity:list")
    @GetMapping("/list/all")
    public R<List<PowAgentCityVo>> list(PowAgentCityBo bo) {
        return R.ok(powAgentCityService.queryList(bo));
    }

    /**
     * 新增代理商城市
     */
    @SaCheckPermission("power:powerAgentCity:add")
    @Log(title = "代理商城市", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentCityBo bo) {
        return toAjax(powAgentCityService.insertByBo(bo));
    }
    /**
     * 批量新增代理商城市
     */
    @SaCheckPermission("power:powerAgentCity:batchAdd")
    @Log(title = "代理商城市", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batchAdd")
    public R<Void> batchAdd(@RequestBody PowAgentCityBatchBo batchBo) {
        return toAjax(powAgentCityService.insertByBoToList(batchBo));
    }
}
