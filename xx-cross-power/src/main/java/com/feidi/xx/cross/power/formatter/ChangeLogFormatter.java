package com.feidi.xx.cross.power.formatter;

/**
 * 数据变更日志格式化器接口
 * 用于格式化数据变更日志消息，支持模板字符串和SpEL表达式
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface ChangeLogFormatter {

    /**
     * 格式化变更日志消息
     * 
     * @param template 模板字符串，支持占位符和SpEL表达式
     *                 例如：将 {main} 的司机组从 {afunc(oldValue)} 变更为 {bfunc(newValue)}
     * @param context  变更上下文，包含变更相关的数据
     * @return 格式化后的消息字符串
     */
    String format(String template, ChangeLogContext context);

    /**
     * 格式化变更日志消息（简化版本）
     * 
     * @param template  模板字符串
     * @param main      主体对象（如司机姓名）
     * @param fieldName 字段名称
     * @param oldValue  旧值
     * @param newValue  新值
     * @return 格式化后的消息字符串
     */
    default String format(String template, Object main, String fieldName, Object oldValue, Object newValue) {
        ChangeLogContext context = ChangeLogContext.builder()
                .main(main)
                .fieldName(fieldName)
                .oldValue(oldValue)
                .newValue(newValue)
                .build();
        return format(template, context);
    }
}
