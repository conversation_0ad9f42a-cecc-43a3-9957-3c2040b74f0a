package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.power.domain.PowAgentLine;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 代理商线路视图对象 pow_agent_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAgentLine.class)
public class PowAgentUserLineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 路线
     */
    @ExcelProperty(value = "路线")
    private Long lineId;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentUserId;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;
    private String statusText;


}
