package com.feidi.xx.cross.power.domain.vo;

import com.feidi.xx.cross.power.domain.PowAuditRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 审核记录视图对象 pow_audit_record
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PowAuditRecord.class)
public class PowAuditRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机
     */
    @ExcelProperty(value = "司机")
    private Long driverId;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;

    /**
     * 审核状态 [AuditStatusEnum]
     */
    @ExcelProperty(value = "审核状态 [AuditStatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "DriverAuditStatusEnum")
    private String status;

    /**
     * 审核人
     */
    @ExcelProperty(value = "审核人")
    private String auditUser;

    /**
     * 驳回原因
     */
    private String reasons;

}
