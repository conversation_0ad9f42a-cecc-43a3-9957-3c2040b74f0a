package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.power.api.RemoteAgentRateService;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemoteAgentRateVo;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemotePlatformAgentVo;
import com.feidi.xx.cross.power.domain.PowAgentRate;
import com.feidi.xx.cross.power.mapper.PowAgentRateMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteAgentRateServiceImpl implements RemoteAgentRateService {

    private final PowAgentRateMapper powAgentRateMapper;

    /**
     * 根据代理商id和平台编码查询代理商费率
     *
     * @param agentId      代理商id
     * @param platformCode 平台编码
     * @return 代理商费率
     */
    @Override
    public RemoteAgentRateVo getByAgentIdAndPlatformCode(Long agentId, String platformCode) {
        LambdaQueryWrapper<PowAgentRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentRate::getAgentId, agentId)
                .eq(PowAgentRate::getPlatformCode, PlatformCodeEnum.TY.getCode())
                .orderByDesc(PowAgentRate::getId);
        PowAgentRate powAgentRate = powAgentRateMapper.selectOne(lqw);

        return BeanUtils.copyProperties(powAgentRate, RemoteAgentRateVo.class);
    }

    @Override
    public List<RemotePlatformAgentVo> listByPlatformCode(String platformCode) {
        LambdaQueryWrapper<PowAgentRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentRate::getPlatformCode, PlatformCodeEnum.TY.getCode());
        List<PowAgentRate> powAgentRateList = powAgentRateMapper.selectList(lqw);
        return BeanUtils.copyToList(powAgentRateList, RemotePlatformAgentVo.class);
    }

    @Override
    public List<RemotePlatformAgentVo> listByAgentId(Long agentId) {
        LambdaQueryWrapper<PowAgentRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentRate::getAgentId, agentId);
        List<PowAgentRate> powAgentRateList = powAgentRateMapper.selectList(lqw);
        return BeanUtils.copyToList(powAgentRateList, RemotePlatformAgentVo.class);
    }
}
