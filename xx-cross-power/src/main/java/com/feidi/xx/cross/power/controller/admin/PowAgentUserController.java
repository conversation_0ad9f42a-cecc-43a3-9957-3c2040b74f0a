package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayOutputStream;
import java.util.List;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 代理商用户
 * 前端访问路由地址为:/power/agent/user
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/agent/user")
public class PowAgentUserController extends BaseController {

    private final IPowAgentUserService powAgentUserService;

    /**
     * 查询代理商用户列表
     */
    @SaCheckPermission("power:powerAgentUser:list")
    @GetMapping("/list")
    public TableDataInfo<PowAgentUserVo> list(PowAgentUserBo bo, PageQuery pageQuery) {
        return powAgentUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出代理商用户列表
     */
    @SaCheckPermission("power:powerAgentUser:export")
    @Log(title = "代理商用户", businessType = BusinessType.EXPORT)
    @Download(name="代理商用户",module = ModuleConstants.POWER,mode="no")
    @PostMapping("/export")
    public Object export(PowAgentUserBo bo,HttpServletResponse response) {
        List<PowAgentUserVo> list = powAgentUserService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "代理商用户", PowAgentUserVo.class, outputStream);
        return outputStream.toByteArray();

    }

    /**
     * 获取代理商用户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerAgentUser:query")
    @GetMapping("/{id}")
    public R<PowAgentUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powAgentUserService.queryById(id));
    }

    /**
     * 新增代理商用户
     */
    @SaCheckPermission("power:powerAgentUser:add")
    @Log(title = "代理商用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAgentUserBo bo) {
        return toAjax(powAgentUserService.insertByBo(bo));
    }

    /**
     * 修改代理商用户
     */
    @SaCheckPermission("power:powerAgentUser:edit")
    @Log(title = "代理商用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAgentUserBo bo) {
        return toAjax(powAgentUserService.updateByBo(bo));
    }

    /**
     * 删除代理商用户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerAgentUser:remove")
    @Log(title = "代理商用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powAgentUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
