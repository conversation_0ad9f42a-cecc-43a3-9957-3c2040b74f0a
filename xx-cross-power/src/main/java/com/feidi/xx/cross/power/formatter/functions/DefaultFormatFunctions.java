package com.feidi.xx.cross.power.formatter.functions;

import com.feidi.xx.cross.power.formatter.ChangeLogFunction;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 默认格式化函数集合
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Component
public class DefaultFormatFunctions {

    /**
     * quote - 添加引号
     */
    @Component
    public static class QuoteFormatter implements ChangeLogFunction {
        @Override
        public String getFunctionName() {
            return "quote";
        }

        @Override
        public String format(Object value) {
            return "\"" + (value != null ? value : "") + "\"";
        }
    }

    /**
     * bool - 格式化布尔值
     */
    @Component
    public static class BoolFormatter implements ChangeLogFunction {
        @Override
        public String getFunctionName() {
            return "bool";
        }

        @Override
        public String format(Object value) {
            if (value instanceof Boolean) {
                return (Boolean) value ? "是" : "否";
            }
            if (value instanceof String) {
                String str = ((String) value).toLowerCase();
                return "true".equals(str) || "1".equals(str) ? "是" : "否";
            }
            if (value instanceof Number) {
                return ((Number) value).intValue() != 0 ? "是" : "否";
            }
            return "否";
        }
    }

    /**
     * date - 格式化日期
     */
    @Component
    public static class DateFormatter implements ChangeLogFunction {
        @Override
        public String getFunctionName() {
            return "date";
        }

        @Override
        public String format(Object value) {
            if (value instanceof Date) {
                return new SimpleDateFormat("yyyy-MM-dd").format((Date) value);
            }
            if (value instanceof Long) {
                return new SimpleDateFormat("yyyy-MM-dd").format(new Date((Long) value));
            }
            return value != null ? value.toString() : "";
        }

        @Override
        public String format(Object value, Object... params) {
            if (params.length > 0 && params[0] instanceof String pattern) {
                if (value instanceof Date) {
                    return new SimpleDateFormat(pattern).format((Date) value);
                }
                if (value instanceof Long) {
                    return new SimpleDateFormat(pattern).format(new Date((Long) value));
                }
            }
            return format(value);
        }
    }

    /**
     * upper - 转换为大写
     */
    @Component
    public static class UpperFormatter implements ChangeLogFunction {
        @Override
        public String getFunctionName() {
            return "upper";
        }

        @Override
        public String format(Object value) {
            return value != null ? value.toString().toUpperCase() : "";
        }
    }

    /**
     * lower - 转换为小写
     */
    @Component
    public static class LowerFormatter implements ChangeLogFunction {
        @Override
        public String getFunctionName() {
            return "lower";
        }

        @Override
        public String format(Object value) {
            return value != null ? value.toString().toLowerCase() : "";
        }
    }
}
