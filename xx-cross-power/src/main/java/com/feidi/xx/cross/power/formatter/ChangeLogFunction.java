package com.feidi.xx.cross.power.formatter;

/**
 * 变更日志格式化函数接口
 * 实现此接口的Spring Bean会被自动注册到格式化器中
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface ChangeLogFunction {

    /**
     * 获取函数名称，用于在模板中引用
     * 例如返回 "myFunc"，则可在模板中使用 {#myFunc.format(value)}
     * 
     * @return 函数名称
     */
    String getFunctionName();

    /**
     * 格式化值
     * 
     * @param value 要格式化的值
     * @return 格式化后的字符串
     */
    String format(Object value);

    /**
     * 格式化值（带参数）
     * 
     * @param value  要格式化的值
     * @param params 额外参数
     * @return 格式化后的字符串
     */
    default String format(Object value, Object... params) {
        return format(value);
    }
}
