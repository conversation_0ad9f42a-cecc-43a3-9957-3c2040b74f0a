package com.feidi.xx.cross.power.synchronize.doamin;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 车辆对象 px_car
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("px_car")
public class PxCar extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 车牌号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carNumber;

    /**
     * 车辆品牌
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carBrand;

    /**
     * 车辆型号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carModel;

    /**
     * 车辆颜色
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carColor;

    /**
     * 车辆样式
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carStyle;

    /**
     * 驱动方式
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String driveType;

    /**
     * 车辆识别码（车架号）
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String vin;

    /**
     * 发动机编号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String engine;

    /**
     * 经度
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String longitude;

    /**
     * 纬度
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String latitude;

    /**
     * 座位数
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Integer seat;

    /**
     * 车辆图片
     */
    private String carPic;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
