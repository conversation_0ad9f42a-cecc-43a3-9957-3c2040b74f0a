package com.feidi.xx.cross.power.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.service.IPowDriverReview;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机审核
 * 前端访问路由地址为:/power/driver/Review
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/Review")
public class PowDriverReviewController extends BaseController {

    private final IPowDriverReview powDriverReview;

    /**
     * 查询需要审核司机列表
     * @param bo 查询条件
     * @param pageQuery 分页条件
     * @return 需要审核的司机列表
     */
    @SaCheckPermission("power:powerDriverReview:reviewList")
    @GetMapping("/reviewList")
    @Id2NameAspect
    public TableDataInfo<PowDriverVo> reviewList(PowDriverBo bo, PageQuery pageQuery) {
        // 不是司机
        bo.setIdentity(DriverIdentityEnum.NO.getCode());
        return powDriverReview.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机审核列表
     */
    @SaCheckPermission("power:powerDriverReview:export")
    @Log(title = "司机审核列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowDriverBo bo, HttpServletResponse response) {
        List<PowDriverVo> list = powDriverReview.queryList(bo);
        ExcelUtil.exportExcel(list, "司机审核列表", PowDriverVo.class, response);
    }

    /**
     * 获取审核司机详情
     * @return
     */
    @SaCheckPermission("power:powerDriverReview:review")
    @RepeatSubmit()
    @GetMapping("/review/{id}")
    @Id2NameAspect
    public R<PowDriverVo> reviewDetail(@NotNull(message = "司机不能为空")
                                            @PathVariable Long id) {
        return R.ok(powDriverReview.reviewDetail(id));
    }

    /**
     * 审核司机
     * @param bo 审核条件
     * @return
     */
    @SaCheckPermission("power:powerDriverReview:review")
    @Log(title = "司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/review")
    public R<Void> review( @RequestBody PowDriverBo bo) {
        return toAjax(powDriverReview.review(bo));
    }
}
