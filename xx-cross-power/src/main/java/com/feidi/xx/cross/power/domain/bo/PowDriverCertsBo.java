package com.feidi.xx.cross.power.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.power.domain.PowDriverCerts;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 司机证件业务对象 pow_driver_certs
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowDriverCerts.class, reverseConvertGenerate = false)
public class PowDriverCertsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 上级
     */
    @NotNull(message = "上级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 证件所有人
     */
    private String certsOwner;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * ossId
     */
    @NotNull(message = "ossId不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;

    /**
     * 第一次领证时间
     */
    private Date firstTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态
     */
    private String status;


}
