package com.feidi.xx.cross.power.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowCarBo;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机端 - 车辆管理
 * 前端访问路由地址为:/power/drv
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX+"/car")
public class DrvCarController extends BaseController {

    /**
     *  获取车辆
     * @return
     */
    @GetMapping()
    public R<PowCarVo> getCar() {
        return R.ok();
    }

    /**
     *  编辑
     *
     * @return
     */
    @RepeatSubmit()
    @PutMapping("/{id}")
    public R edit(@Validated(EditGroup.class) @RequestBody PowCarBo bo) {
        return R.ok();
    }
}
