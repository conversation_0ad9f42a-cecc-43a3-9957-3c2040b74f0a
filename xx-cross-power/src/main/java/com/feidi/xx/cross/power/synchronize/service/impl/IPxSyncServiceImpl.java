package com.feidi.xx.cross.power.synchronize.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.cross.power.synchronize.doamin.*;
import com.feidi.xx.cross.power.synchronize.mapper.*;
import com.feidi.xx.cross.power.synchronize.service.IPxSyncService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 线上旧数据查询类
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
@DS("cross")
public class IPxSyncServiceImpl implements IPxSyncService {

    private final PxDriverMapper pxDriverMapper;
    private final PxDriverAccountMapper pxDriverAccountMapper;
    private final PxCarMapper pxCarMapper;
    private final PxDriverCertsMapper pxDriverCertsMapper;
    private final PxAgentMapper pxAgentMapper;

    @Override
    public List<PxDriver> getDriverList() {
        return pxDriverMapper.selectList();
    }

    @Override
    public List<PxDriver> listByDriverIds(List<Long> driverIds) {
        LambdaQueryWrapper<PxDriver> lqw = new LambdaQueryWrapper<>();
        lqw.in(PxDriver::getId, driverIds);
        return pxDriverMapper.selectList(lqw);
    }

    @Override
    public List<PxDriverAccount> getDriverAccountList() {
        return pxDriverAccountMapper.selectList();
    }

    @Override
    public List<PxCar> getCarList() {
        return pxCarMapper.selectList();
    }

    @Override
    public List<PxDriverCerts> getDriverCertsList() {
        return pxDriverCertsMapper.selectList();
    }

    @Override
    public List<PxAgent> getAgentList() {
        return pxAgentMapper.selectList();
    }
}