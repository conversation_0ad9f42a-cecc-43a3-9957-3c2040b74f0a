package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.power.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowAuditRecordVo;
import com.feidi.xx.cross.power.domain.bo.PowAuditRecordBo;
import com.feidi.xx.cross.power.service.IPowAuditRecordService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 审核记录
 * 前端访问路由地址为:/power/driver/review
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver/review")
public class PowAuditRecordController extends BaseController {

    private final IPowAuditRecordService powAuditRecordService;

    /**
     * 查询审核记录列表
     */
    @SaCheckPermission("power:powerAuditRecord:list")
    @GetMapping("/list")
    public TableDataInfo<PowAuditRecordVo> list(PowAuditRecordBo bo, PageQuery pageQuery) {
        return powAuditRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出审核记录列表
     */
    @SaCheckPermission("power:powerAuditRecord:export")
    @Log(title = "审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PowAuditRecordBo bo, HttpServletResponse response) {
        List<PowAuditRecordVo> list = powAuditRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "审核记录", PowAuditRecordVo.class, response);
    }

    /**
     * 获取审核记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerAuditRecord:query")
    @GetMapping("/{id}")
    public R<PowAuditRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powAuditRecordService.queryById(id));
    }

    /**
     * 新增审核记录
     */
    @SaCheckPermission("power:powerAuditRecord:add")
    @Log(title = "审核记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowAuditRecordBo bo) {
        return toAjax(powAuditRecordService.insertByBo(bo));
    }

    /**
     * 修改审核记录
     */
    @SaCheckPermission("power:powerAuditRecord:edit")
    @Log(title = "审核记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowAuditRecordBo bo) {
        return toAjax(powAuditRecordService.updateByBo(bo));
    }

    /**
     * 删除审核记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerAuditRecord:remove")
    @Log(title = "审核记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powAuditRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
