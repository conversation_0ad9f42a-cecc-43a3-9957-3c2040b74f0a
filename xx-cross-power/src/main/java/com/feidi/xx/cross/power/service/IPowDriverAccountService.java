package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverAccountBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 司机账户Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowDriverAccountService {

    /**
     * 查询司机账户
     *
     * @param id 主键
     * @return 司机账户
     */
    PowDriverAccountVo queryById(Long id);

    /**
     * 分页查询司机账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机账户分页列表
     */
    TableDataInfo<PowDriverAccountVo> queryPageList(PowDriverAccountBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机账户列表
     *
     * @param bo 查询条件
     * @return 司机账户列表
     */
    List<PowDriverAccountVo> queryList(PowDriverAccountBo bo);

    /**
     * 新增司机账户
     *
     * @param bo 司机账户
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverAccountBo bo);

    /**
     * 修改司机账户
     *
     * @param bo 司机账户
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverAccountBo bo);

    /**
     * 校验并批量删除司机账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据司机id查询司机账户信息
     *
     * @param driverId 司机id
     * @return 司机账户信息
     */
    PowDriverAccountVo queryByDriverId(Long driverId);

    /**
     * 根据司机id查询司机账户信息
     *
     * @param driverIds 司机ids
     * @return 司机账户信息
     */
    List<PowDriverAccountVo> queryByDriverIds(List<Long> driverIds);

    /**
     * 根据司机id修改司机账户信息
     *
     * @param account 账户
     * @param accountName 账户名称
     * @param driverId 司机id
     */
    boolean updateAccountAndNameByDriverId(String account, String accountName, Long driverId);
}
