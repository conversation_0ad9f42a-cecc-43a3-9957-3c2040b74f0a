package com.feidi.xx.cross.power.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 司机组对象 pow_group
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pow_group")
public class PowGroup extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商主体名称
     */
    private String mainBody;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 图标
     */
    private String icon;

    /**
     * 类型[DriverGroupTypeEnum 0默认 1正常]
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否同步[IsYesEnum]
     */
    private String sync;


    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
