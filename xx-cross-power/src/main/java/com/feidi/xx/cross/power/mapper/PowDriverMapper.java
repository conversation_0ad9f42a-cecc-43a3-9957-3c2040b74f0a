package com.feidi.xx.cross.power.mapper;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 司机Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowDriverMapper extends BaseMapperPlus<PowDriver, PowDriverVo> {
    /**
     * 禁用司机
     * @param id
     * @return
     */
    default int sealById(Long id){
        return update(Wrappers.<PowDriver>lambdaUpdate()
                .eq(PowDriver::getId, id)
                .set(PowDriver::getStatus, UserStatusEnum.DISABLE.getCode())
        );
    }

    /**
     * 解禁司机
     * @param id
     * @return
     */
    default int unsealById(Long id){
        return update(Wrappers.<PowDriver>lambdaUpdate()
                .eq(PowDriver::getId, id)
                .set(PowDriver::getStatus, UserStatusEnum.OK.getCode())
        );
    }

    default PowDriver getByPhone(String phone) {
        return selectOne(Wrappers.<PowDriver>lambdaQuery().eq(PowDriver::getPhone, phone));
    }

    default PowDriver getByCode(String code) {
        return selectOne(Wrappers.<PowDriver>lambdaQuery().eq(PowDriver::getCode, code));
    }

    default List<PowDriver> getByName(String name) {
        return selectList(Wrappers.<PowDriver>lambdaQuery().like(PowDriver::getName, name));
    }

    default List<PowDriver> listByGroupId(Long... groupId) {
        return selectList(Wrappers.<PowDriver>lambdaQuery().in(PowDriver::getGroupId, Arrays.asList(groupId)));
    }
    default List<PowDriver> listByAgentId(Long agentId) {
        return selectList(Wrappers.<PowDriver>lambdaQuery()
                .eq(agentId != null, PowDriver::getAgentId, agentId)
        );
    }

    default List<PowDriver> listByAgentIds(Collection<Long> agentIds) {
        return selectList(Wrappers.<PowDriver>lambdaQuery()
                .in(CollUtil.isNotEmpty(agentIds), PowDriver::getAgentId, agentIds)
                .eq(PowDriver::getAuditStatus, DrvAuditStatusEnum.SUCCESS.getCode())
        );
    }

    /**
     * 修改交易密码
     * @param driverId 司机密码
     * @param rawPwd 未加密的密码
     * @return
     */
    default int updatePwd(Long driverId, String rawPwd) {
        return update(Wrappers.<PowDriver>lambdaUpdate()
                .eq(PowDriver::getId, driverId)
                .set(PowDriver::getCapitalPassword, BCrypt.hashpw(rawPwd, BCrypt.gensalt()))
        );
    }

    /**
     * 修改 某个字段
     * @param driverId 司机id
     * @param function 字段
     * @param value 字段值
     * @return
     */
    default <T> int updateAny(Long driverId, SFunction<PowDriver, T> function, T value) {
        return update(Wrappers.<PowDriver>lambdaUpdate()
                .eq(PowDriver::getId, driverId)
                .set(function, value)
        );
    }

    IPage<PowDriver> selectByUnionId(@Param("bo") PowDriverBo bo, IPage<?> page);

    List<PowDriver> selectCountByBo(@Param("bo")PowDriverBo bo);
}
