package com.feidi.xx.cross.power.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.cross.power.domain.PowCar;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;

/**
 * 车辆Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface PowCarMapper extends BaseMapperPlus<PowCar, PowCarVo> {
    default PowCar getByDriverId(Long driverId) {
        return selectOne(new LambdaQueryWrapper<PowCar>().eq(PowCar::getDriverId, driverId));
    }

    default List<PowCar> listByDriverIds(Collection<Long> driverIds) {
        return selectList(new LambdaQueryWrapper<PowCar>().in(PowCar::getDriverId, driverIds));
    }

    default Integer updateByDriverId(PowCar car){
        return update(car, new LambdaQueryWrapper<PowCar>().eq(PowCar::getDriverId, car.getDriverId()));
    }
}
