package com.feidi.xx.cross.power.synchronize.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.constant.TenantConstants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvCertTypeEnum;
import com.feidi.xx.cross.power.domain.*;
import com.feidi.xx.cross.power.mapper.*;
import com.feidi.xx.cross.power.synchronize.doamin.*;
import com.feidi.xx.cross.power.synchronize.service.IPxSyncService;
import com.feidi.xx.cross.power.synchronize.service.IPowSyncService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运力数据同步实现类
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class IPowSyncServiceImpl implements IPowSyncService {

    private static final Logger log = LoggerFactory.getLogger(IPowSyncServiceImpl.class);
    private final PowDriverMapper powDriverMapper;
    private final PowDriverRateMapper powDriverRateMapper;
    private final PowAuditRecordMapper powAuditRecordMapper;
    private final PowDriverCertsMapper powDriverCertMapper;
    private final PowDriverAccountMapper powDriverAccountMapper;
    private final PowCarMapper powCarMapper;
    private final PowAgentMapper powAgentMapper;
    private final IPxSyncService pxSyncService;

    /**
     * 司机数据同步
     */
    @Override
    public Boolean driverSync() {
        //读取xx-cross库px_driver表原司机数据
        List<PxDriver> pxDrivers = pxSyncService.getDriverList();
        List<PowDriver> powDrivers = BeanUtils.copyToList(pxDrivers, PowDriver.class);
        powDriverMapper.insertBatch(powDrivers);
        //插入数据库
        pxDrivers.forEach(pxDriver -> {
            //需要另外处理的字段
            BigDecimal rate = pxDriver.getRate();
            String reason = pxDriver.getReason();
            String isReceive = pxDriver.getIsReceive();
            //rate字段插入对应司机pow_driver_rate表
            if (pxDriver.getRate() != null) {
                PowDriverRate powDriverRate = new PowDriverRate();
                powDriverRate.setDriverId(pxDriver.getId());
                powDriverRate.setRate(rate);
                powDriverRate.setPlatformCode(PlatformCodeEnum.TY.getCode());
                powDriverRateMapper.insert(powDriverRate);
            }
            //reason插入对应audit_record表
            if (pxDriver.getReason() != null) {
                PowAuditRecord powAuditRecord = new PowAuditRecord();
                powAuditRecord.setDriverId(pxDriver.getId());
                powAuditRecord.setReasons(reason);
                if (pxDriver.getAgentId() != null) {
                    powAuditRecord.setAgentId(pxDriver.getAgentId());
                }
                powAuditRecord.setStatus(DrvAuditStatusEnum.REJECT.getCode());
                powAuditRecordMapper.insert(powAuditRecord);
            } else {
                PowAuditRecord powAuditRecord = new PowAuditRecord();
                powAuditRecord.setDriverId(pxDriver.getId());
                if (pxDriver.getAgentId() != null) {
                    powAuditRecord.setAgentId(pxDriver.getAgentId());
                }
                powAuditRecord.setStatus(DrvAuditStatusEnum.SUCCESS.getCode());
                powAuditRecordMapper.insert(powAuditRecord);
            }
            //isReceive手动插入pow_driver
            LambdaUpdateWrapper<PowDriver> lqw = new LambdaUpdateWrapper<>();
            lqw.set(PowDriver::getReceive, isReceive).eq(PowDriver::getId, pxDriver.getId());
            powDriverMapper.update(lqw);
        });
        log.info("司机数据同步成功");
        return true;
    }

    /**
     * 司机账户同步
     */
    @Override
    public Boolean driverAccountSync() {
        List<PxDriverAccount> driverAccountList = pxSyncService.getDriverAccountList();
        Map<Long, String> requestMap =  driverAccountList.stream().collect(Collectors.toMap(PxDriverAccount::getDriverId, PxDriverAccount::getIsDefault));
        List<PowDriverAccount> powDriverAccounts = BeanUtils.copyToList(driverAccountList, PowDriverAccount.class);
        powDriverAccounts.forEach(driverAccount -> {
            driverAccount.setDefaulted(requestMap.get(driverAccount.getDriverId()));
        });
        powDriverAccountMapper.insertBatch(powDriverAccounts);
        log.info("司机账户同步成功");
        return true;
    }

    /**
     * 车辆信息同步
     */
    @Override
    public Boolean carSync() {
        List<PxCar> carList = pxSyncService.getCarList();
        List<PowCar> powCars = BeanUtils.copyToList(carList, PowCar.class);
        //过滤carList中的driverId查AgentId
        List<Long> driverIds = carList.stream().map(PxCar::getDriverId).toList();
        Map<Long, Long> driverIdAgentIdMap = pxSyncService.listByDriverIds(driverIds).stream().collect(Collectors.toMap(PxDriver::getId, PxDriver::getAgentId));
        powCars.forEach(car -> {
            car.setAgentId(driverIdAgentIdMap.get(car.getDriverId()));
        });
        powCarMapper.insertBatch(powCars);
        log.info("车辆信息同步成功");
        return true;
    }

    /**
     * 司机证件同步
     */
    @Override
    public Boolean driverCertsSync() {
        List<PxDriverCerts> driverCertsList = pxSyncService.getDriverCertsList();
        List<PowDriverCerts> powDriverCerts = BeanUtils.copyToList(driverCertsList, PowDriverCerts.class);
        List<Long> driverIds = driverCertsList.stream().map(PxDriverCerts::getDriverId).toList();
        //去重ids,用于手动插入数据
        List<Long> ids = driverCertsList.stream()
                .map(PxDriverCerts::getDriverId)
                .distinct()  // 去重核心操作
                .toList();
        List<PxDriver> pxDrivers = pxSyncService.listByDriverIds(driverIds);
        Map<Long, String> resultMap = pxDrivers.stream().collect(Collectors.toMap(PxDriver::getId, PxDriver::getName));
        powDriverCerts.forEach(driverCert -> {
            driverCert.setCertsOwner(resultMap.get(driverCert.getDriverId()));
        });
        //插入两条type为7和8的数据
        ids.forEach(driverId -> {
            for (int i = 0; i < 2; i++) {
                PowDriverCerts driverCerts = new PowDriverCerts();
                driverCerts.setCertsOwner(resultMap.get(driverId));
                driverCerts.setDriverId(driverId);
                if (i==0) {
                    driverCerts.setType(DrvCertTypeEnum.LICENSE_BACK.getCode());
                }else {
                    driverCerts.setType(DrvCertTypeEnum.DRIVING_BACK.getCode());
                }
                driverCerts.setOssId(1L);
                driverCerts.setStatus(StatusEnum.ENABLE.getCode());
                driverCerts.setTenantId(TenantConstants.DEFAULT_TENANT_ID);
                powDriverCertMapper.insert(driverCerts);
            }
        });
        powDriverCertMapper.insertBatch(powDriverCerts);
        log.info("司机证件同步成功");
        return true;
    }

    @Override
    public Boolean agentSync() {
        List<PxAgent> agentList = pxSyncService.getAgentList();
        Map<Long, PxAgent> resultMap = agentList.stream().collect(Collectors.toMap(PxAgent::getId, Function.identity()));
        List<PowAgent> powAgents = BeanUtils.copyToList(agentList, PowAgent.class);
        powAgents.forEach(agent -> {
            agent.setLicense(String.valueOf(resultMap.get(agent.getId()).getBusinessLicense()));
            agent.setCompanyName(resultMap.get(agent.getId()).getMainBody());
            agent.setTaxNo(resultMap.get(agent.getId()).getCreditCode());
            agent.setLegalPerson(resultMap.get(agent.getId()).getName());
        });
        powAgentMapper.insertBatch(powAgents);
        return true;
    }
}
