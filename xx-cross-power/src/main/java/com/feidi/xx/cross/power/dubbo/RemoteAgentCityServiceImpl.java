package com.feidi.xx.cross.power.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.power.api.RemoteAgentCityService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;
import com.feidi.xx.cross.power.domain.PowAgentCity;
import com.feidi.xx.cross.power.mapper.PowAgentCityMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代理商城市服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteAgentCityServiceImpl implements RemoteAgentCityService {

    private final PowAgentCityMapper baseMapper;


    @Override
    public List<Long> getCityIdByCompanyName(String companyName) {
        if (companyName != null) {
            LambdaQueryWrapper<PowAgentCity> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(PowAgentCity::getCompanyName, companyName);
            //获取id列表
            List<Long> ids = baseMapper.selectList(wrapper).stream().map(PowAgentCity::getCityId).toList();

            return ids;
        }
        return List.of();
    }

    @Override
    public List<String> getCompanyNameByCityId(Long id) {
        if (id != null) {
            LambdaQueryWrapper<PowAgentCity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PowAgentCity::getCityId, id);
            //获取id列表
            List<String> companyName = baseMapper.selectList(wrapper).stream().map(PowAgentCity::getCompanyName).toList();

            return companyName;
        }
        return List.of();
    }

    /**
     * 根据代理商id和城市id获取代理商城市信息
     * @param agentId
     * @param cityId
     * @return
     */
    @Override
    public RemoteAgentCityVo getAgentCityInfoByAgentIdAndCityId(Long agentId,Long cityId) {
        LambdaQueryWrapper<PowAgentCity> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentCity::getAgentId,agentId)
                .eq(PowAgentCity::getCityId,cityId)
                .orderByDesc(PowAgentCity::getCreateTime)
                .last(Constants.LIMIT_ONE);
        PowAgentCity powAgentCity = baseMapper.selectOne(lqw);
        return BeanUtils.copyProperties(powAgentCity,RemoteAgentCityVo.class);
    }

    /**
     * 根据agentId获取代理商城市信息
     *
     * @param agentId 代理商id
     * @return 代理商城市信息集合
     */
    @Override
    public List<RemoteAgentCityVo> getAgentCityInfoByAgentId(Long agentId) {
        LambdaQueryWrapper<PowAgentCity> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentCity::getAgentId,agentId);
        List<PowAgentCity> powAgentCities = baseMapper.selectList(lqw);
        return BeanUtils.copyToList(powAgentCities, RemoteAgentCityVo.class);
    }
}
