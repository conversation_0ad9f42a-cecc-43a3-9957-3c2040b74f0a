package com.feidi.xx.cross.power.domain.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.validate.ReviewGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 司机申请身份信息业务对象
 * <AUTHOR>
 */
@Data
@AutoMapper(target = PowDriver.class, reverseConvertGenerate = false)
public class PowDriverAgreementForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 加盟类型[DriverTypeEnum]
     */
    private String type;

    /**
     * 代扣协议图片
     */
    private Long agreementOssId;

    /**
     * 手持身份证图片
     */
    private Long heldIdCardOssId;

    /**
     * 手持代扣协议图片
     */
    private Long heldAgreementOssId;

    /**
     * 驳回原因
     */
    private List<String> reasons;

}
