package com.feidi.xx.cross.power.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.power.domain.PowGroup;
import com.feidi.xx.cross.power.domain.bo.PowGroupBo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;

import java.util.Collection;
import java.util.List;

/**
 * 司机组Service接口
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface IPowGroupService {

    /**
     * 查询司机组
     *
     * @param id 主键
     * @return 司机组
     */
    PowGroupVo queryById(Long id);

    /**
     * 分页查询司机组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机组分页列表
     */
    TableDataInfo<PowGroupVo> queryPageList(PowGroupBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机组列表
     *
     * @param bo 查询条件
     * @return 司机组列表
     */
    <T> List<T> queryList(PowGroupBo bo, Class<T> clazz);

    /**
     * 新增司机组
     *
     * @param bo 司机组
     * @return 是否新增成功
     */
    Boolean insertByBo(PowGroupBo bo);

    /**
     * 修改司机组
     *
     * @param bo 司机组
     * @return 是否修改成功
     */
    Boolean updateByBo(PowGroupBo bo);

    /**
     * 校验并批量删除司机组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据主键查询司机组信息，并缓存
     *
     * @param ids 主键集合
     * @return 司机组信息
     */
    List<PowGroup> queryByIdsAndCacheInfo(List<Long> ids);

    /**
     * 根据主键查询司机组信息
     *
     * @param groupId 司机组主键
     * @return 司机组信息
     */
    PowGroup getGroupInfo(Long groupId);

    /**
     * 根据主键查询司机组信息
     *
     * @param groupIds 主键集合
     * @return 司机组信息
     */
    List<PowGroup> getGroupInfo(List<Long> groupIds);
}
