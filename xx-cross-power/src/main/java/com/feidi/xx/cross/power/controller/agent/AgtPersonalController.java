package com.feidi.xx.cross.power.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.encrypt.annotation.ApiEncrypt;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.power.domain.bo.PowAgentBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PasswordForm;
import com.feidi.xx.cross.power.domain.pojo.vo.PowPersonalForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.service.IPowAgentService;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 代理商 - 个人中心
 * 前端访问路由地址为:/power/agent
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/personal")
public class AgtPersonalController extends BaseController {
    private final IPowAgentService powAgentService;
    private final IPowAgentUserService powAgentUserService;

    /**
     * 获取账号详细信息
     */
    @GetMapping()
    public R<PowAgentVo> getInfo() {
        return R.ok(powAgentService.queryById(LoginHelper.getUserId()));
    }

    /**
     * 修改个人信息
     * @param bo
     * @return
     */
    @PutMapping()
    public R update(@Validated(EditGroup.class) @RequestBody PowPersonalForm bo) {
        /// 修改账号信息
        PowAgentBo powAgentBo = BeanUtils.copyProperties(bo, PowAgentBo.class);
        powAgentBo.setId(LoginHelper.getAgentId());
        powAgentService.updateByBo(powAgentBo);
        return R.ok();
    }

    /**
     * 重置密码
     *
     * @param bo 新旧密码
     */
    @ApiEncrypt
    @PutMapping("/updatePwd")
    public R<Boolean> updatePwd(@Validated @RequestBody PasswordForm bo) {
        return R.ok(powAgentUserService.resetPwd(bo));
    }

    /**
     * 代理商邀请码
     */
    @GetMapping("/invite")
    public R<PowAgentUserVo> generateInviteCode() {
        return R.ok(powAgentUserService.generateInviteCode());
    }

}
