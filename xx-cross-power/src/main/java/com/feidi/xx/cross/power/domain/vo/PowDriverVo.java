package com.feidi.xx.cross.power.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.SourceEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.common.id2name.annotation.Id2NameMore;
import com.feidi.xx.cross.common.enums.power.DriveTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.power.domain.PowDriver;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverAgreementVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverCarVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverIdCardVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDrvDrivingVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 司机视图对象 pow_driver
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@Accessors(chain = true)
@ExcelIgnoreUnannotated
@AutoMapper(target = PowDriver.class)
public class PowDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;



    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 分组
     */
    @ExcelProperty(value = "分组")
    private Long groupId;
    private String groupName;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String cardNo;

    /**
     * 性别
     */
    //@ExcelProperty(value = "性别")
    //@ExcelDictFormat(dictType = "sys_user_sex")
    private String sex;
    private String sexText;

    /**
     * 司机类型
     */
    @ExcelProperty(value = "司机类型", converter = ExcelEnumConvert.class)
    //@ExcelDictFormat(dictType = "DriveTypeEnum")
    @ExcelEnumFormat(enumClass = DriveTypeEnum.class)
    private String type;
    private String typeText;

    /**
     * 城市编码
     */
    @ExcelProperty(value = "城市编码")
    @Id2Name(fullName="residentCity",index = "cityCode")
    private String cityCode;

    /**
     * 准驾车型
     */
    @ExcelProperty(value = "准驾车型")
    private String approvedType;

    /**
     * 审核状态
     */
    //@ExcelProperty(value = "审核状态")
    //@ExcelDictFormat(dictType = "DriverAuditStatusEnum")
    private String auditStatus;
    private String auditStatusText;

    /**
     * 是否接单
     */
    @ExcelProperty(value = "是否接单", converter = ExcelEnumConvert.class)
    //@ExcelDictFormat(dictType = "IsYesEnum")
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String receive;
    private String receiveText;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelEnumConvert.class)
    //@ExcelDictFormat(dictType = "UserStatusEnum")
    @ExcelEnumFormat(enumClass = UserStatusEnum.class)
    private String status;
    private String statusText;

    /**
     * 身份
     */
    @ExcelProperty(value = "身份", converter = ExcelEnumConvert.class)
    //@ExcelDictFormat(dictType = "DriverIdentityEnum")
    @ExcelEnumFormat(enumClass = DriverIdentityEnum.class)
    private String identity;
    private String identityText;

    /**
     * 是否合规
     */
    @ExcelProperty(value = "是否合规", converter = ExcelEnumConvert.class)
    //@ExcelDictFormat(dictType = "IsYesEnum")
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String legal;
    private String legalText;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 提交渠道
     */
    @Enum2Text(enumClass = SourceEnum.class)
    @ExcelProperty(value = "来源", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = SourceEnum.class)
    private String source;
    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 审核人
     */
    private String auditUser;

    /**
     * 司机信息
     */
    @Id2NameMore
    private PowDriverVo driverVo;

    /**
     * 身份证信息
     */
    private PowDriverIdCardVo idCardVo;

    /**
     * 驾驶证信息
     */
    private PowDrvDrivingVo drivingVo;

    /**
     * 车辆信息
     */
    private PowDriverCarVo carVo;

    /**
     * 代扣协议信息
     */
    private PowDriverAgreementVo agreementVo;

    /**
     * 驳回原因
     */
    private List<String> rejectReason;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 司机组类型
     */
    private String groupType;

    /**
     * 父代理商id
     */
    private Long parentId;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 驾龄
     */
    private Integer driveYear;

    /**
     * 是否接单
     */
    private String isReceive;

    /**
     * 是否需要设置交易密码 true是 false否
     */
    private boolean setPwd;

    /**
     * 可提现余额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;

    /**
     * 总金额
     */
    private Long amount;

    /**
     * 新token
     */
    private String refreshToken;

    /**
     * 驳回理由
     */
    private String reason;

    @ExcelProperty(value = "上级代理商名称")
    private String parentAgentName;

    /**
     * 邀请地址
     */
    @ExcelProperty(value = "招商地址")
    private String inviteUrl;

    /**
     * 邀请二维码
     */
    @ExcelProperty(value = "招商广告")
    private String inviteImage;

    /**
     * 司机车辆信息
     */
    private PowCarVo car;

    /**
     * 司机证件列表
     */
    private List<PowDriverCertsVo> certs;

    /**
     * 地址
     */
    private String address;

    /**
     * 资产密码
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String capitalPassword;

    /**
     * 支付版账号
     */
    private String account;

    /**
     * 支付宝姓名
     */
    private String accountName;

    /**
     * 有效期截至日期
     */
    private Date endTime;

    /**
     * 身份证(正页)
     */
    private Long frontOssId;

    /**
     * 身份证(副页)
     */
    private Long backOssId;

    /**
     * 身份证正页url
     */
    private String frontImg;

    /**
     * 身份证副页url
     */
    private String backImg;

    /**
     * 人脸id
     */
    private Long faceOssId;

    /**
     * 人脸图片url
     */
    private String faceImg;

    /**
     * openId
     */
    private String openId;

    /**
     * 订单转卖服务费比例
     */
    private BigDecimal resellServiceRate;

    /**
     * 待出行订单数
     */
    private Long pendingTripOrdersNum = 0L;

    /**
     * 已接单和行程中订单数
     */
    private Long receiveIngOrdersNum = 0L;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 司机订单
     */
    private List<PowDriverOrderVo> driverOrders;
}
