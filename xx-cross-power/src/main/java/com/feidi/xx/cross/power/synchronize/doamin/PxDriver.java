package com.feidi.xx.cross.power.synchronize.doamin;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PtSourceEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "px_driver", autoResultMap = true)
public class PxDriver extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分组id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private Long groupId;

    /**
     * 父代理商id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private Long parentId;

    /**
     * 代理商id
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private Long agentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String cardNo;

    /**
     * 生日
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String birthday;

    /**
     * 性别
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String sex;

    /**
     * 资产密码
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String capitalPassword;

    /**
     * 驾龄
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private Integer driveYear;

    /**
     * 佣金比例
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private BigDecimal rate;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否接单 {@link IsYesEnum}
     */
    private String isReceive;

    /**
     * 状态 {@link UserStatusEnum}
     */
    private String status;

    /**
     * 身份
     */
    private String identity;

    /**
     * 来源 {@link PtSourceEnum}
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 车牌号
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS)
    private String carNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 驳回理由
     */
    @TableField(insertStrategy = FieldStrategy.ALWAYS, updateStrategy = FieldStrategy.ALWAYS)
    private String reason;
}
