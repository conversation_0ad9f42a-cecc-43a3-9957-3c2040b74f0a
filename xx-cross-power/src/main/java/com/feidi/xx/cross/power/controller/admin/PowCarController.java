package com.feidi.xx.cross.power.controller.admin;

import java.io.ByteArrayOutputStream;
import java.util.List;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.bo.PowCarBo;
import com.feidi.xx.cross.power.service.IPowCarService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 车辆
 * 前端访问路由地址为:/power/car
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/car")
public class PowCarController extends BaseController {

    private final IPowCarService powCarService;

    /**
     * 查询车辆列表
     */
    @SaCheckPermission("power:powerCar:list")
    @GetMapping("/list")
    public TableDataInfo<PowCarVo> list(PowCarBo bo, PageQuery pageQuery) {
        return powCarService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出车辆列表
     */
    @SaCheckPermission("power:powerCar:export")
    @Log(title = "车辆", businessType = BusinessType.EXPORT)
    @Download(name="车辆",module = ModuleConstants.POWER,mode="no")
    @PostMapping("/export")
    public Object export(PowCarBo bo, HttpServletResponse response) {
        List<PowCarVo> list = powCarService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "车辆", PowCarVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取车辆详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("power:powerCar:query")
    @GetMapping("/{id}")
    public R<PowCarVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(powCarService.queryById(id));
    }

    /**
     * 新增车辆
     */
    @SaCheckPermission("power:powerCar:add")
    @Log(title = "车辆", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PowCarBo bo) {
        return toAjax(powCarService.insertByBo(bo));
    }

    /**
     * 修改车辆
     */
    @SaCheckPermission("power:powerCar:edit")
    @Log(title = "车辆", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PowCarBo bo) {
        return toAjax(powCarService.updateByBo(bo));
    }

    /**
     * 删除车辆
     *
     * @param ids 主键串
     */
    @SaCheckPermission("power:powerCar:remove")
    @Log(title = "车辆", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(powCarService.deleteWithValidByIds(List.of(ids), true));
    }
}
