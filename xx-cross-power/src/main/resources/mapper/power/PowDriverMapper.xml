<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.power.mapper.PowDriverMapper">
    <select id="selectByUnionId" resultType="com.feidi.xx.cross.power.domain.PowDriver">
        select o.*, a.company_name
        from pow_driver o
        left join pow_agent a on o.agent_id = a.id and a.del_flag = 0
        <where>
            o.del_flag = 0
            <if test="bo.unionId != null and bo.unionId != ''">
                and (
                o.name LIKE CONCAT('%', #{bo.unionId}, '%')
                OR o.type LIKE CONCAT('%', #{bo.unionId}, '%')
                OR a.company_name LIKE CONCAT('%', #{bo.unionId}, '%')
                )
            </if>
            <if test="bo.status != null">
                and o.status = #{bo.status}
            </if>
            <if test="bo.auditStatus != null">
                and o.audit_status = #{bo.auditStatus}
            </if>
            <if test="bo.agentId != null and bo.agentId != 0">
                and o.agent_id = #{bo.agentId}
            </if>
            <if test="bo.startTime != null and bo.startTime != ''
                  and bo.endTime != null and bo.endTime != ''">
                and o.submit_time between #{bo.startTime} and #{bo.endTime}
            </if>
        </where>
        order by o.submit_time
    </select>

    <select id="selectCountByBo" resultType="com.feidi.xx.cross.power.domain.PowDriver">
        select o.id, o.audit_status
        from pow_driver o
        left join pow_agent a on o.agent_id = a.id and a.del_flag = 0
        <where>
            o.del_flag = 0
            <if test="bo.unionId != null and bo.unionId != ''">
                and (
                o.name LIKE CONCAT('%', #{bo.unionId}, '%')
                OR o.type LIKE CONCAT('%', #{bo.unionId}, '%')
                OR a.company_name LIKE CONCAT('%', #{bo.unionId}, '%')
                )
            </if>
            <if test="bo.status != null">
                and o.status = #{bo.status}
            </if>
            <if test="bo.auditStatus != null">
                and o.audit_status = #{bo.auditStatus}
            </if>
            <if test="bo.agentId != null and bo.agentId != 0">
                and o.agent_id = #{bo.agentId}
            </if>
            <if test="bo.startTime != null and bo.startTime != ''
                  and bo.endTime != null and bo.endTime != ''">
                and o.submit_time between #{bo.startTime} and #{bo.endTime}
            </if>
            <if test="bo.lastTime != null and bo.lastTime != ''">
                and o.update_time > #{bo.lastTime}
            </if>
        </where>
    </select>
</mapper>
