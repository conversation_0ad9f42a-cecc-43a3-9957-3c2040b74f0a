package com.feidi.xx.cross.common.constant.finance;

/**
 * 财务管理静态常量
 */
public interface FinanceConstants {

    /**
     * 相关缓存前缀
     */
    String CACHE_PREFIX = "fx:";

    /**
     * 提现-自动打款开关
     */
    String CASH_AUTO_OPEN = "fx.cash.auto.open";

    /**
     * 财务-提现-自动打款最大金额（单位：元）
     */
    String CASH_AUTO_MAX = "fx.cash.auto.max";

    /**
     * 提现-单日最大提现频率
     */
    String CASH_FREQUENCY = "fx.cash.max.frequency";

    /**
     * 转账-单日最大转账频率
     */
    String TRANSFER_FREQUENCY = "fx.transfer.max.frequency";

    /**
     * 提现自动处理 缓存队列的KEY
     */
    String CASH_AUTO_CACHE_KEY = CACHE_PREFIX + "LIST:CASH:";

    /**
     * 提现次数超过上限
     */
    String CASH_APPLY_EXCEED_MSG = "每日只能提现{}次，请明天再试";

    /**
     * 转账次数超过上限
     */
    String TRANSFER_APPLY_EXCEED_MSG = "每日只能转账{}次，请明天再试";

    /**
     * 资产密码错误最大次数
     */
    int PASSWORD_WRONG_MAX_TIMES = 5;

    /**
     * 资产密码多次错误提示
     */
    String PASSWORD_LOCK_MSG = "账号存在被盗风险，前往忘记密码进行修改";

    /**
     * 资产密码未设置提示
     */
    String PASSWORD_INIT_MSG = "需要先设置交易密码才能进行提现和转账";

    /**
     * 提现审核中提示
     */
    String CASHING_MSG = "您有一笔提现审核中的记录，审核完成后才能再次提现或转账";

    /**
     * 无流水提示
     */
    String NO_FLOW_MSG = "可提现余额不足";

    /**
     * 可提现余额不足提示
     */
    String CASH_OUT_MSG = "可提现余额不足";

    /**
     * 钱包禁用提示
     */
    String WALLET_DISABLE = "钱包已被停用,当前禁止提现或转账";

    /**
     * 是否启用代理商付款
     */
    String SYS_AGENT_PAY = "sys.agent.pay";

    /**
     * 版本发布状态【Y.发布中|N.未发布】
     */
    String SYSTEM_VERSION_RELEASE = "sys.version.release";

    /**
     * 自营司机提现校验提示
     */
    String PROPRIETARY_EXPERIENCE_DISABLING_PROMPT = "非常抱歉，自营司机无法提现哟，请联系所属运力商进行结算";

    /**
     * 财务参数配置 - 自动打款开关
     */
    String CASH_AUTO_SWITCH = "fin.cash.auto.switch";

    /**
     * 财务参数配置 - 自动打款最大金额
     */
    String CASH_AUTO_PAY_MAX = "fin.cash.auto.max";
}
