package com.feidi.xx.cross.common.constant.operate;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 运营服务 缓存常量
 *
 * <AUTHOR>
 */
public interface OperateCacheConstants {

    /**
     * 运营服务相关缓存前缀
     */
    String CACHE_PREFIX = "opr:";

    /**
     * 平台信息
     */
    String PLATFORM_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "platform:";

    /**
     * 平台信息
     */
    String PLATFORM_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "platform:" + ":appKey:";

    /**
     * 产品信息
     */
    String PRODUCT_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "product:";

    /**
     * 平台信息
     */
    String LINE_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "line:";

    /**
     * 价格信息
     */
    String PRICING_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "price:";


    /**
     * 平台产品信息
     */
    String PLAT_PROD_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "platform:product";

    /**
     * 节假日缓存
     */
    String HOLIDAY_KEY =GlobalConstants.GLOBAL_REDIS_KEY+ CACHE_PREFIX + "holiday:";

    /**
     * 城市信息缓存
     */
    String CITY_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "city:";

    /**
     * 城市价格信息缓存
     */
    String CITY_PRICE_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "city-price:";


    /**
     * 电子围栏信息缓存
     */
    String FENCE_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "fence:";

    /**
     * 询价记录缓存前缀
     */
    String ESTIMATE_RECORD_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "estimate-record:";

    /**
     * 价格信息缓存前缀
     */
    String PRICE_INFO_PREFIX =GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX +"price-info:" ;


    /**
     * 行政区划信息
     */
    String DISTRICT_INFO_PREFIX =GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"district-info:" ;

    /**
     * 节假日
     */
    String HOLIDAY="holiday";
    /**
     * 名字
     */
    String NAME="name";
    /**
     * 日期
     */
    String DATE="date";

    /**
     * 代客下单价格浮动比例
     */
   String  PROXY_ORDER_PRICE_FLUCTUATION_RATE = "proxy_order_price_fluctuation_rate";

    /**
     * 司机代客下单收益比例
     */
   String  DRIVER_PROXY_ORDER_PROFIT_RATE = "driver_proxy_order_profit_rate";

   String MIN_DISTANCE = "minimum.distance";

    /**
     * 福州-福清电子围栏id
     */
   String FENCE_FZ_FQ_IDS = "opr.fence.fz.fq.ids";

    /**
     * 福州-福清运营时间
     */
   String FENCE_FZ_FQ_TIME="opr.fence.fz.fq.time";

    /**
     * 福清-福州电子围栏id
     */
   String FENCE_FQ_FZ_IDS = "opr.fence.fq.fz.ids";

    /**
     * 福清-福州运营时间
     */
   String FENCE_FQ_FZ_TIME = "opr.fence.fq.fz.time";
}
