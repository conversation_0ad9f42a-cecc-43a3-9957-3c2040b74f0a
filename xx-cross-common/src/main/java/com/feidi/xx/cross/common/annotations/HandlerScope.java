package com.feidi.xx.cross.common.annotations;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 处理器模式注解，可继承
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Scope
public @interface HandlerScope {

    @AliasFor(annotation = Scope.class, attribute = "value")
    String value() default ConfigurableBeanFactory.SCOPE_SINGLETON;

}
