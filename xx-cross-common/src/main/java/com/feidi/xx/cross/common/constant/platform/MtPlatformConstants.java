package com.feidi.xx.cross.common.constant.platform;

/**
 * 美团系统配置key
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
public interface MtPlatformConstants {

    /**
     * 取消费预留时间
     */
    String MT_CANCEL_RETAIN_TIME = "cancel.retain.time";

    /**
     * 取消费接单后预留时间 单位：分钟
     */
    String MT_CANCEL_CHARGING_TIME = "cancel.charging.time";

    /**
     * 取消费最小值
     */
    String MT_CANCEL_MIN_AMOUNT = "cancel.min.amount";

    /**
     * 取消费最大值
     */
    String MT_CANCEL_MAX_AMOUNT = "cancel.max.amount";

    /**
     * 乘客取消扣费比例
     */
    String MT_CANCEL_CHARGING_RATE = "cancel.charging.rate";

    /**
     * 取消费默认最小值
     */
    String MT_DEFAULT_MIN_AMOUNT = "5";

    /**
     * 取消费默认最大值
     */
    String MT_DEFAULT_MAX_AMOUNT = "20";
}
