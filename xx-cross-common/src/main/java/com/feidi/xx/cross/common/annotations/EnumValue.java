package com.feidi.xx.cross.common.annotations;

import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.reflect.ReflectUtils;
import com.google.common.base.CaseFormat;
import jakarta.validation.*;

import java.lang.annotation.*;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = EnumValue.Validator.class)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@ReportAsSingleViolation
public @interface EnumValue {
    String message() default "枚举值不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends Enum<?>> value();

    boolean convertCase() default false;

    String compareField() default "info";

    CaseFormat from() default CaseFormat.UPPER_UNDERSCORE;

    CaseFormat to() default CaseFormat.LOWER_CAMEL;

    class Validator implements ConstraintValidator<EnumValue, String> {
        private EnumValue enumValue;

        @Override
        public void initialize(EnumValue enumValue) {
            this.enumValue = enumValue;
        }

        @Override
        public boolean isValid(String text, ConstraintValidatorContext constraintValidatorContext) {
            if (StringUtils.isEmpty(text)) {
                return true;
            }

            return Arrays.stream(enumValue.value().getEnumConstants())
                    .anyMatch(e -> {
                        Object codeValue = ReflectUtils.invokeGetter(e, enumValue.compareField());
                        /*String name = e.name();
                        if (enumValue.convertCase()) {
                            name = enumValue.from().to(enumValue.to(), name);
                        }*/
                        return codeValue.equals(text);
                    });
        }
    }
}

