package com.feidi.xx.cross.operate.api.domain.city.vo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 城市价格关联对象 opr_city_price
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RemoteCityPriceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市表id
     */
    private Long cityId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * { PlatformCodeEnum}
     */
    private String platformCode;



}
