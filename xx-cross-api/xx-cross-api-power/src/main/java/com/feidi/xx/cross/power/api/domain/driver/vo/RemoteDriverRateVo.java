package com.feidi.xx.cross.power.api.domain.driver.vo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  司机佣金比例
 * <AUTHOR>
 */
@Data
public class RemoteDriverRateVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
