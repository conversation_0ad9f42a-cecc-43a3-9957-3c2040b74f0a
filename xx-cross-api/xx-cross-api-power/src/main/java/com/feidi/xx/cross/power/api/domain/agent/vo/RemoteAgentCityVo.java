package com.feidi.xx.cross.power.api.domain.agent.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteAgentCityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;
    /**
     * 代理商名称
     */
    private String companyName;

    /**
     * 开城id
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}
