package com.feidi.xx.cross.power.api.domain.agent.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemotePayConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户余额
     */
    private Long amount;

    /**
     * 累计支出金额
     */
    private Long expend;

    /**
     * AppId
     */
    private String appId;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * App 证书 地址
     */
    private String appCertPath;

    /**
     * alipay 证书 地址
     */
    private String alipayCertPath;

    /**
     * 根 证书 地址
     */
    private String rootCertPath;

    /**
     * 状态 0开启 1禁用
     */
    private String status;
}
