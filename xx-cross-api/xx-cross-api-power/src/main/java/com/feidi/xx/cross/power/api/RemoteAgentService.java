package com.feidi.xx.cross.power.api;


import com.feidi.xx.common.core.domain.model.LoginUser;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;

import java.util.List;

/**
 * 代理商服务
 *
 * <AUTHOR>
 */
public interface RemoteAgentService {

    /**
     * 代理信息获取
     *
     * @param agentId 代理ID
     * @return 代理信息
     */
    RemoteAgentVo getAgentInfoById(Long agentId);

    /**
     * 批量查询代理信息
     * @param agentIds
     * @return
     */
    List<RemoteAgentVo> getAgentInfoById(List<Long> agentIds);

    /**
     * 批量查询子代理信息
     * @param agentId
     * @return
     */
    List<RemoteAgentVo> getChildAgentInfoById(Long agentId);

    /**
     * 代理商登录
     *
     * @param username
     * @param tenantId
     * @return 登陆信息
     */
     LoginUser getAgentInfo(String username, String tenantId);

    /**
     * 获取所有代理商信息
     * @return
     */
    List<RemoteAgentVo> getAllAgentInfo();
}
