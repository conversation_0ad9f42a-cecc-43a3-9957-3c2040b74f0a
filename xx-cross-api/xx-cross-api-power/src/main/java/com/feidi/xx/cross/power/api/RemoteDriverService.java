package com.feidi.xx.cross.power.api;


import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDrvLoginVo;

import java.util.List;

/**
 * 司机服务
 *
 * <AUTHOR>
 */
public interface RemoteDriverService {

    /**
     * 司机登录注册
     *
     * @param remoteDrvLoginVo
     * @return 司机
     */
    XcxLoginUser login(RemoteDrvLoginVo remoteDrvLoginVo);

    /**
     * 获取司机信息
     *
     * @param driverId 司机ID
     * @return 司机
     */
    RemoteDriverVo getDriverInfo(Long driverId);

    /**
     * 获取司机信息
     *
     * @param code 邀请码
     * @return 司机
     */
    RemoteDriverVo getDriverInfoByCode(String code);

    /**
     * 批量获取司机信息
     *
     * @param driverIds 司机ID集合
     * @return 司机集合
     */
    List<RemoteDriverVo> getDriverInfo(List<Long> driverIds);


    /**
     * 批量获取司机信息 模糊查询
     * @param bo
     * @return
     */
    List<RemoteDriverVo> queryDriverInfo(RemoteDriverQueryBo bo);

    /**
     * 获取司机信息及车辆信息
     *
     * @param driverId 司机ID
     * @return 司机、车辆信息
     */
    RemoteDriverVo getDriverAndCar(Long driverId);

    /**
     * 获取司机信息（仅获取司机信息，不查询相关代理和佣金比例）
     *
     * @param driverId 司机id
     * @return
     */
    RemoteDriverVo getDriverByDriverId(Long driverId);

    /**
     * 司机注销
     *
     * @param driverId
     * @return
     */
    boolean signOut(Long driverId);

    /**
     * 修改司机状态
     */
    boolean updateStatus(Long driverId,String status);

    /**
     * 根据司机id查询司机信息
     *
     * @param driverIds 司机id集合
     * @return 司机信息
     */
    List<RemoteDriverVo> getDriverByIds(List<Long> driverIds);

    /**
     * 根据司机手机号查询司机信息
     *
     * @param phones 司机手机号集合
     * @return 司机信息
     */
    List<RemoteDriverVo> listDriverByPhones(List<String> phones);

    /**
     * 查询所有司机信息
     *
     * @return 司机信息
     */
    List<RemoteDriverVo> queryByDriverStatus(List<String> driverStatuses);

    /**
     * 司机位置上报
     * @param userId
     * @param longitude
     * @param latitude
     */
    void locReport(Long userId, Double longitude, Double latitude);
}
