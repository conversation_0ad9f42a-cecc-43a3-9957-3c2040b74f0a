package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentUserVo;

/**
 * 代理商用户服务
 */
public interface RemoteAgentUserService {

    /**
     * 根据邀请码查询代理商信息
     * @param code
     * @return
     */
    RemoteAgentUserVo getAgentInfoByCode(String code);

    /**
     * 代理商id查询代理商信息
     * @param agentId
     * @return
     */
    RemoteAgentUserVo getAgentInfoByAgentId(Long agentId);

    /**
     * 代理商用户id查询代理商用户信息
     *
     * @param agentUserId 代理商用户id
     * @return 代理商用户信息
     */
    RemoteAgentUserVo getAgentUserInfoById(Long agentUserId);
}
