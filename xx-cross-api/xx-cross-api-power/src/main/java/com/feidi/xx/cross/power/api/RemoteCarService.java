package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;

import java.util.List;

/**
 * 远程车辆服务
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface RemoteCarService {

    /**
     * 根据车辆ID查询车辆信息
     *
     * @param carId 车辆id
     * @return 车辆信息
     */
    RemoteCarVo getCarInfoById(Long carId);

    /**
     * 根据司机ID查询车辆信息
     *
     * @param driverId 司机id
     * @return 车辆信息
     */
    RemoteCarVo getCarInfoByDriverId(Long driverId);

    /**
     * 根据司机ID集合查询车辆信息
     *
     * @param driverIds
     * @return 车辆信息
     */
    List<RemoteCarVo> queryByDriverIds(List<Long> driverIds);
}
