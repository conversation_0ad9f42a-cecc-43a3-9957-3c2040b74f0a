package com.feidi.xx.cross.power.api.domain.driver.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class RemoteDriverQueryBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模糊查询 id name phone
     */
    private String unionId;

    /**
     * 司机id
     */
    private String id;

    /**
     * 司机名称
     */
    private String name;

    /**
     * 司机手机号
     */
    private String phone;

    /**
     * 所属代理商id
     */
    private List<Long> agentIds;

    /**
     * 审核状态
     */
    private String auditStatus;

}
