package com.feidi.xx.cross.power.api.domain.agentrate.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 代理商佣金比例视图对象
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
public class RemoteAgentRateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
