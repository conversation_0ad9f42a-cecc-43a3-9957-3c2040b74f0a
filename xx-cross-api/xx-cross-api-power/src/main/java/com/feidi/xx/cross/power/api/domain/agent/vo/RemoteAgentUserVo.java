package com.feidi.xx.cross.power.api.domain.agent.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteAgentUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色[管理员|调度|财务]
     */
    private String role;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
