package com.feidi.xx.cross.power.api.domain.driver.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RemoteDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;

    /**
     * openId
     */
    private String openId;

    /**
     * 分组
     */
    private Long groupId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 代理商手机号
     */
    private String agentPhone;

    /**
     * 上级
     */
    private Long parentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private String sex;

    /**
     * 资产密码
     */
    private String capitalPassword;

    /**
     * 司机类型
     */
    private String type;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 准驾车型
     */
    private String approvedType;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否接单
     */
    private String receive;

    /**
     * 状态
     */
    private String status;

    /**
     * 身份
     */
    private String identity;

    /**
     * 是否合规
     */
    private String legal;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 车辆信息
     */
    private RemoteCarVo remoteCarVo;

    /**
     * 客服电话
     */
    private String servicesPhone;

    /**
     * 订单转卖服务费比例
     */
    private BigDecimal resellServiceRate;

}
