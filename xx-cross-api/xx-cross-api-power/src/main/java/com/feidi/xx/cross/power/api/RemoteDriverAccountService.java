package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverAccountVo;

import java.util.Collection;
import java.util.List;

/**
 * 司机账户服务
 *
 * <AUTHOR>
 */
public interface RemoteDriverAccountService {

    /**
     * 获取司机账户详情
     *
     * @param accountId 账户ID
     * @return 司机账户
     */
    RemoteDriverAccountVo getDriverAccount(Long accountId);

    /**
     * 获取司机账户列表
     * @param driverIds
     * @return
     */
    List<RemoteDriverAccountVo> getDriverAccounts(Collection<Long> driverIds);

}
