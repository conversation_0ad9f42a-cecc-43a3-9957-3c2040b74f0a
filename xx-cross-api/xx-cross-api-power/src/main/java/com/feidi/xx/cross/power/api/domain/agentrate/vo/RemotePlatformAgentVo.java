package com.feidi.xx.cross.power.api.domain.agentrate.vo;

import com.feidi.xx.common.core.enums.StatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台-代理关系
 */
@Data
public class RemotePlatformAgentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 2L;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 启用状态 {@link StatusEnum}
     */
    private String status;
}
