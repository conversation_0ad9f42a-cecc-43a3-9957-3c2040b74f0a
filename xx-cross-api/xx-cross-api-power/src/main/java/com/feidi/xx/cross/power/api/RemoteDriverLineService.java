package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverLineVo;

import java.util.List;

/**
 * 司机线路服务
 *
 * <AUTHOR>
 */
public interface RemoteDriverLineService {

    /**
     * 获取司机线路
     * @param driverId 司机ID
     * @return 线路
     */
    List<Long> getDriverLine(Long driverId);

    /**
     * 司机是否有这个线路
     * @param driverId 司机ID
     * @param lineId 线路ID
     * @return boolean
     */
    Boolean driverHasLine(Long driverId, Long lineId);

    /**
     * 根据司机id获取司机线路信息
     *
     * @param driverId 司机id
     * @return 司机线路信息集合
     */
    List<RemoteDriverLineVo> getByDriverId(Long driverId);

    /**
     * 获取司机线路信息
     * @param driverId
     * @param lineId
     * @return
     */
    RemoteDriverLineVo getByDriverIdAndLineId(Long driverId, Long lineId);
}
