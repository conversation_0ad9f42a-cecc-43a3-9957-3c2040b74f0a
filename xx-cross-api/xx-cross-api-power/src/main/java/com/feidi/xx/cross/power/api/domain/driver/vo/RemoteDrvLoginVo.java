package com.feidi.xx.cross.power.api.domain.driver.vo;

import com.feidi.xx.common.core.enums.PtLoginTypeEnum;
import com.feidi.xx.common.core.enums.SourceEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 *  司机登录注册参数
 * <AUTHOR>
 */
@Data
public class RemoteDrvLoginVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 小程序ID
     */
    private String appId;

    /**
     * openId
     */
    private String openId;

    /**
     * Uid
     */
    private String unionId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     *邀请类型
     */
    private String inviteType;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机组id
     */
    private Long groupId;

    /**
     * 登录类型
     */
    private PtLoginTypeEnum type;

    /**
     * 客户端key
     */
    private String clientKey;

    /**
     * 推送ID
     */
    private String pushId;
}
