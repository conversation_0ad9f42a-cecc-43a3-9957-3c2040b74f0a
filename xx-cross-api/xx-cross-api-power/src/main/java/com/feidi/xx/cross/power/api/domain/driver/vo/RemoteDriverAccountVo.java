package com.feidi.xx.cross.power.api.domain.driver.vo;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentTypeEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.xx.MoneyConvertUtils;
import lombok.Data;

import java.io.Serializable;

/**
 *  司机账户
 * <AUTHOR>
 */
@Data
public class RemoteDriverAccountVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号
     */
    private String account;

    /**
     * 类型
     */
    private String type;
    private String typeText;
    public String getTypeText() {
        return PaymentTypeEnum.getInfoByCode(this.type);
    }

    /**
     * 是否默认
     */
    private String isDefault;
    private String isDefaultText;
    public String getIsDefaultText() {
        return IsYesEnum.getInfoByCode(this.isDefault);
    }

    /**
     * 状态
     */
    private String status;
    private String statusText;
    public String getStatusText() {
        return StatusEnum.getInfoByCode(this.status);
    }

    /**
     * 验证资金
     */
    private Long verifyAmount;
    private String verifyAmountText;
    public String getVerifyAmountText() {
        return MoneyConvertUtils.fen2YuanStr(verifyAmount);
    }


    /**
     * 备注
     */
    private String remark;
}
