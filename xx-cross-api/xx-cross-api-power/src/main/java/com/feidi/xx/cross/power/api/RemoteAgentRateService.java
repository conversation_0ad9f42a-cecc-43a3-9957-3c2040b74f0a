package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemoteAgentRateVo;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemotePlatformAgentVo;

import java.util.List;

/**
 * 远程代理商费率服务
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface RemoteAgentRateService {
    /**
     * 根据代理商id和平台编码查询代理商费率
     *
     * @param agentId 代理商id
     * @param platformCode 平台编码
     * @return 代理商费率
     */
    RemoteAgentRateVo getByAgentIdAndPlatformCode(Long agentId, String platformCode);

    /**
     * 根据平台查询
     * @param platformCode
     * @return
     */
    List<RemotePlatformAgentVo> listByPlatformCode(String platformCode);

    /**
     * 根据代理id查询
     * @param agentId
     * @return
     */
    List<RemotePlatformAgentVo> listByAgentId(Long agentId);
}
