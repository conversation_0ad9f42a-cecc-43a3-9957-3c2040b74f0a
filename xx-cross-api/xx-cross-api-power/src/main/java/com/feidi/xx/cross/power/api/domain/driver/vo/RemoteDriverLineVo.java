package com.feidi.xx.cross.power.api.domain.driver.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
/**
 *  司机线路
 * <AUTHOR>
 */
@Data
public class RemoteDriverLineVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 主线路ID
     */
    private Long mainLineId;
}
