package com.feidi.xx.cross.power.api.domain.group.vo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  司机组
 * <AUTHOR>
 */
@Data
public class RemoteGroupVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商主体名称
     */
    private String mainBody;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 佣金比例
     */
    private BigDecimal rate;

    /**
     * 图标
     */
    private String icon;

    /**
     * 类型[DriverGroupTypeEnum 0默认 1正常]
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
