package com.feidi.xx.cross.power.api.domain.line.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 线路代理
 */
@Data
public class RemoteLineAgentBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线路ID
     */
    @NotNull(message = "线路不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 代理ID
     */
    @Size(min = 1, message = "代理不能为空", groups = { AddGroup.class, EditGroup.class })
    @NotNull(message = "代理不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<Long> agentIds;


}
