package com.feidi.xx.cross.power.api.domain.line.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 线路代理
 */
@Data
public class RemoteAgentLineBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 父代理商ID
     */
    private Long parentId;

    /**
     * 代理商ID
     */
    @NotNull(message = "代理不能为空")
    private Long agentId;

    /**
     * 代理ID
     */
//    @Size(min = 1, message = "线路不能为空")
    private List<Long> lineIds;

}
