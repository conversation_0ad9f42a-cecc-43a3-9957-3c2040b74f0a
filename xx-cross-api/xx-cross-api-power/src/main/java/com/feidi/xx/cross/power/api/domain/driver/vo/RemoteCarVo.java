package com.feidi.xx.cross.power.api.domain.driver.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteCarVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆品牌
     */
    private String carBrand;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆颜色
     */
    private String carColor;

    /**
     * 车辆图片
     */
    private String carImage;

    /**
     * 车辆样式
     */
    private String carType;

    /**
     * 驱动方式
     */
    private String driveType;

    /**
     * 车辆识别码（车架号）
     */
    private String vin;

    /**
     * 发动机编号
     */
    private String engine;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 剩余座位数
     */
    private Integer surplusSeat;

    /**
     * 状态
     */
    private String status;
}
