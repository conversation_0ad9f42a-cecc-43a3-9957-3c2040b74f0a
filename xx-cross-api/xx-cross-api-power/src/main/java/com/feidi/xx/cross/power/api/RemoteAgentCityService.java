package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;

import java.util.List;

/**
 * 代理商城市服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemoteAgentCityService {

    List<Long> getCityIdByCompanyName(String companyName);

    List<String> getCompanyNameByCityId(Long id);

    RemoteAgentCityVo getAgentCityInfoByAgentIdAndCityId(Long agentId,Long cityId);

    /**
     * 根据agentId获取代理商城市信息
     *
     * @param agentId 代理商id
     * @return 代理商城市信息集合
     */
    List<RemoteAgentCityVo> getAgentCityInfoByAgentId(Long agentId);
}
